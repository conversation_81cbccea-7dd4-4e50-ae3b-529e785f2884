# Instacart Market Basket Analysis - Requirements
# Core Data Science Libraries
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# Machine Learning
scikit-learn>=1.0.0
xgboost>=1.5.0
tensorflow>=2.6.0
keras>=2.6.0

# Association Rules Mining
mlxtend>=0.19.0

# Data Visualization
plotly-dash>=2.0.0
bokeh>=2.4.0

# Web Development
flask>=2.0.0
dash>=2.0.0
gunicorn>=20.1.0

# Jupyter Notebooks
jupyter>=1.0.0
ipykernel>=6.0.0
notebook>=6.4.0

# Data Processing
scipy>=1.7.0
statsmodels>=0.12.0

# Utilities
tqdm>=4.62.0
requests>=2.26.0

# Optional: Power BI Integration
# powerbiclient>=3.0.0

# Development Tools
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0
