{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\App.js\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Layout from './components/Layout/Layout';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport Inventory from './pages/Inventory';\nimport SalesOrders from './pages/SalesOrders';\nimport Suppliers from './pages/Suppliers';\nimport Reports from './pages/Reports';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/inventory\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredPermission: \"inventory.read\",\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/sales-orders\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredPermission: \"orders.read\",\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(SalesOrders, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/suppliers\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredPermission: \"suppliers.read\",\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Suppliers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/reports\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          requiredPermission: \"reports.read\",\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "Layout", "ProtectedRoute", "<PERSON><PERSON>", "Dashboard", "Inventory", "SalesOrders", "Suppliers", "Reports", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "requiredPermission", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport Layout from './components/Layout/Layout';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport Inventory from './pages/Inventory';\nimport SalesOrders from './pages/SalesOrders';\nimport Suppliers from './pages/Suppliers';\nimport Reports from './pages/Reports';\nimport Settings from './pages/Settings';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Routes>\n        {/* Public routes */}\n        <Route path=\"/login\" element={<Login />} />\n\n        {/* Protected routes */}\n        <Route path=\"/\" element={\n          <ProtectedRoute>\n            <Layout>\n              <Navigate to=\"/dashboard\" replace />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        <Route path=\"/dashboard\" element={\n          <ProtectedRoute>\n            <Layout>\n              <Dashboard />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        <Route path=\"/inventory\" element={\n          <ProtectedRoute requiredPermission=\"inventory.read\">\n            <Layout>\n              <Inventory />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        <Route path=\"/sales-orders\" element={\n          <ProtectedRoute requiredPermission=\"orders.read\">\n            <Layout>\n              <SalesOrders />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        <Route path=\"/suppliers\" element={\n          <ProtectedRoute requiredPermission=\"suppliers.read\">\n            <Layout>\n              <Suppliers />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        <Route path=\"/reports\" element={\n          <ProtectedRoute requiredPermission=\"reports.read\">\n            <Layout>\n              <Reports />\n            </Layout>\n          </ProtectedRoute>\n        } />\n\n        {/* Catch all route */}\n        <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n      </Routes>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,YAAY;IAAAa,QAAA,eACXF,OAAA,CAACd,MAAM;MAAAgB,QAAA,gBAELF,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACR,KAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3CR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,GAAG;QAACC,OAAO,eACrBJ,OAAA,CAACT,cAAc;UAAAW,QAAA,eACbF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACZ,QAAQ;cAACqB,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,YAAY;QAACC,OAAO,eAC9BJ,OAAA,CAACT,cAAc;UAAAW,QAAA,eACbF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACP,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,YAAY;QAACC,OAAO,eAC9BJ,OAAA,CAACT,cAAc;UAACoB,kBAAkB,EAAC,gBAAgB;UAAAT,QAAA,eACjDF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACN,SAAS;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,eAAe;QAACC,OAAO,eACjCJ,OAAA,CAACT,cAAc;UAACoB,kBAAkB,EAAC,aAAa;UAAAT,QAAA,eAC9CF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACL,WAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,YAAY;QAACC,OAAO,eAC9BJ,OAAA,CAACT,cAAc;UAACoB,kBAAkB,EAAC,gBAAgB;UAAAT,QAAA,eACjDF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACJ,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,UAAU;QAACC,OAAO,eAC5BJ,OAAA,CAACT,cAAc;UAACoB,kBAAkB,EAAC,cAAc;UAAAT,QAAA,eAC/CF,OAAA,CAACV,MAAM;YAAAY,QAAA,eACLF,OAAA,CAACH,OAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGJR,OAAA,CAACb,KAAK;QAACgB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACZ,QAAQ;UAACqB,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACI,EAAA,GA7DQX,GAAG;AA+DZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}