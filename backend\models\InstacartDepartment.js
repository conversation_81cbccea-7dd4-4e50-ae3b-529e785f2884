const mongoose = require('mongoose');

const departmentSchema = new mongoose.Schema({
  department_id: {
    type: Number,
    required: true,
    unique: true
  },
  department: {
    type: String,
    required: true,
    trim: true
  }
}, {
  timestamps: true,
  collection: 'instacart_departments'
});

// Index for better query performance
departmentSchema.index({ department_id: 1 });
departmentSchema.index({ department: 1 });

module.exports = mongoose.model('InstacartDepartment', departmentSchema);
