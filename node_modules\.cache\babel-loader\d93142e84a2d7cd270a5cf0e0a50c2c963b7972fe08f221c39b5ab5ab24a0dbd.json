{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Inventory\\\\AddInventoryModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, Loader } from 'lucide-react';\nimport { productsAPI, suppliersAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddInventoryModal = ({\n  isOpen,\n  onClose,\n  product = null,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [formData, setFormData] = useState({\n    sku: '',\n    name: '',\n    category: '',\n    price: '',\n    cost: '',\n    quantity: '',\n    minStock: '',\n    maxStock: '',\n    supplier: '',\n    description: '',\n    location: {\n      warehouse: '',\n      aisle: '',\n      shelf: '',\n      bin: ''\n    },\n    reorderPoint: '',\n    reorderQuantity: '',\n    tags: ''\n  });\n  const categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys', 'Footwear'];\n  useEffect(() => {\n    if (isOpen) {\n      fetchSuppliers();\n      if (product) {\n        var _product$supplier, _product$location, _product$location2, _product$location3, _product$location4, _product$tags;\n        // Edit mode - populate form with existing product data\n        setFormData({\n          sku: product.sku || '',\n          name: product.name || '',\n          category: product.category || '',\n          price: product.price || '',\n          cost: product.cost || '',\n          quantity: product.quantity || '',\n          minStock: product.minStock || '',\n          maxStock: product.maxStock || '',\n          supplier: ((_product$supplier = product.supplier) === null || _product$supplier === void 0 ? void 0 : _product$supplier._id) || product.supplier || '',\n          description: product.description || '',\n          location: {\n            warehouse: ((_product$location = product.location) === null || _product$location === void 0 ? void 0 : _product$location.warehouse) || '',\n            aisle: ((_product$location2 = product.location) === null || _product$location2 === void 0 ? void 0 : _product$location2.aisle) || '',\n            shelf: ((_product$location3 = product.location) === null || _product$location3 === void 0 ? void 0 : _product$location3.shelf) || '',\n            bin: ((_product$location4 = product.location) === null || _product$location4 === void 0 ? void 0 : _product$location4.bin) || ''\n          },\n          reorderPoint: product.reorderPoint || '',\n          reorderQuantity: product.reorderQuantity || '',\n          tags: ((_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.join(', ')) || ''\n        });\n      } else {\n        // Add mode - reset form\n        setFormData({\n          sku: '',\n          name: '',\n          category: '',\n          price: '',\n          cost: '',\n          quantity: '',\n          minStock: '',\n          maxStock: '',\n          supplier: '',\n          description: '',\n          location: {\n            warehouse: '',\n            aisle: '',\n            shelf: '',\n            bin: ''\n          },\n          reorderPoint: '',\n          reorderQuantity: '',\n          tags: ''\n        });\n      }\n    }\n  }, [isOpen, product]);\n  const fetchSuppliers = async () => {\n    try {\n      const response = await suppliersAPI.getAll({\n        status: 'active'\n      });\n      setSuppliers(response.data.suppliers || []);\n    } catch (error) {\n      console.error('Failed to fetch suppliers:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    // Here you would typically send the data to your API\n    console.log('Form submitted:', formData);\n    onClose();\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: \"Add New Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"text-gray-400 hover:text-gray-600\",\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"SKU\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"sku\",\n                  value: formData.sku,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"category\",\n                  value: formData.category,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Category\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 163,\n                    columnNumber: 21\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category,\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Product Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Price ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Cost ($)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"cost\",\n                  value: formData.cost,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Quantity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"quantity\",\n                  value: formData.quantity,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Min Stock Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"minStock\",\n                  value: formData.minStock,\n                  onChange: handleChange,\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Supplier\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"supplier\",\n                value: formData.supplier,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                required: true,\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Supplier\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: supplier,\n                  children: supplier\n                }, supplier, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"description\",\n                value: formData.description,\n                onChange: handleChange,\n                rows: 3,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: onClose,\n                className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\",\n                children: \"Add Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(AddInventoryModal, \"uiRB6dTzwbg7b70g5VC2zEA5y/c=\");\n_c = AddInventoryModal;\nexport default AddInventoryModal;\nvar _c;\n$RefreshReg$(_c, \"AddInventoryModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Loader", "productsAPI", "suppliersAPI", "toast", "jsxDEV", "_jsxDEV", "AddInventoryModal", "isOpen", "onClose", "product", "onSuccess", "_s", "loading", "setLoading", "suppliers", "setSuppliers", "formData", "setFormData", "sku", "name", "category", "price", "cost", "quantity", "minStock", "maxStock", "supplier", "description", "location", "warehouse", "aisle", "shelf", "bin", "reorderPoint", "reorderQuantity", "tags", "categories", "fetchSuppliers", "_product$supplier", "_product$location", "_product$location2", "_product$location3", "_product$location4", "_product$tags", "_id", "join", "response", "getAll", "status", "data", "error", "console", "handleChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "log", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "required", "map", "step", "rows", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Inventory/AddInventoryModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, Loader } from 'lucide-react';\nimport { productsAPI, suppliersAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst AddInventoryModal = ({ isOpen, onClose, product = null, onSuccess }) => {\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [formData, setFormData] = useState({\n    sku: '',\n    name: '',\n    category: '',\n    price: '',\n    cost: '',\n    quantity: '',\n    minStock: '',\n    maxStock: '',\n    supplier: '',\n    description: '',\n    location: {\n      warehouse: '',\n      aisle: '',\n      shelf: '',\n      bin: ''\n    },\n    reorderPoint: '',\n    reorderQuantity: '',\n    tags: ''\n  });\n\n  const categories = [\n    'Electronics',\n    'Clothing',\n    'Books',\n    'Home & Garden',\n    'Sports',\n    'Toys',\n    'Footwear'\n  ];\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchSuppliers();\n      if (product) {\n        // Edit mode - populate form with existing product data\n        setFormData({\n          sku: product.sku || '',\n          name: product.name || '',\n          category: product.category || '',\n          price: product.price || '',\n          cost: product.cost || '',\n          quantity: product.quantity || '',\n          minStock: product.minStock || '',\n          maxStock: product.maxStock || '',\n          supplier: product.supplier?._id || product.supplier || '',\n          description: product.description || '',\n          location: {\n            warehouse: product.location?.warehouse || '',\n            aisle: product.location?.aisle || '',\n            shelf: product.location?.shelf || '',\n            bin: product.location?.bin || ''\n          },\n          reorderPoint: product.reorderPoint || '',\n          reorderQuantity: product.reorderQuantity || '',\n          tags: product.tags?.join(', ') || ''\n        });\n      } else {\n        // Add mode - reset form\n        setFormData({\n          sku: '',\n          name: '',\n          category: '',\n          price: '',\n          cost: '',\n          quantity: '',\n          minStock: '',\n          maxStock: '',\n          supplier: '',\n          description: '',\n          location: {\n            warehouse: '',\n            aisle: '',\n            shelf: '',\n            bin: ''\n          },\n          reorderPoint: '',\n          reorderQuantity: '',\n          tags: ''\n        });\n      }\n    }\n  }, [isOpen, product]);\n\n  const fetchSuppliers = async () => {\n    try {\n      const response = await suppliersAPI.getAll({ status: 'active' });\n      setSuppliers(response.data.suppliers || []);\n    } catch (error) {\n      console.error('Failed to fetch suppliers:', error);\n    }\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    // Here you would typically send the data to your API\n    console.log('Form submitted:', formData);\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n        <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={onClose}></div>\n\n        <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n          <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Add New Product</h3>\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    SKU\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"sku\"\n                    value={formData.sku}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Category\n                  </label>\n                  <select\n                    name=\"category\"\n                    value={formData.category}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  >\n                    <option value=\"\">Select Category</option>\n                    {categories.map(category => (\n                      <option key={category} value={category}>{category}</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Product Name\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Price ($)\n                  </label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    name=\"price\"\n                    value={formData.price}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Cost ($)\n                  </label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    name=\"cost\"\n                    value={formData.cost}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Quantity\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"quantity\"\n                    value={formData.quantity}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Min Stock Level\n                  </label>\n                  <input\n                    type=\"number\"\n                    name=\"minStock\"\n                    value={formData.minStock}\n                    onChange={handleChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Supplier\n                </label>\n                <select\n                  name=\"supplier\"\n                  value={formData.supplier}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                  required\n                >\n                  <option value=\"\">Select Supplier</option>\n                  {suppliers.map(supplier => (\n                    <option key={supplier} value={supplier}>{supplier}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleChange}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\"\n                >\n                  Add Product\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddInventoryModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,MAAM,QAAQ,cAAc;AACxC,SAASC,WAAW,EAAEC,YAAY,QAAQ,oBAAoB;AAC9D,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO,GAAG,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC;IACDC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACjB,aAAa,EACb,UAAU,EACV,OAAO,EACP,eAAe,EACf,QAAQ,EACR,MAAM,EACN,UAAU,CACX;EAEDtC,SAAS,CAAC,MAAM;IACd,IAAIS,MAAM,EAAE;MACV8B,cAAc,CAAC,CAAC;MAChB,IAAI5B,OAAO,EAAE;QAAA,IAAA6B,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,aAAA;QACX;QACA1B,WAAW,CAAC;UACVC,GAAG,EAAET,OAAO,CAACS,GAAG,IAAI,EAAE;UACtBC,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAI,EAAE;UACxBC,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,EAAE;UAChCC,KAAK,EAAEZ,OAAO,CAACY,KAAK,IAAI,EAAE;UAC1BC,IAAI,EAAEb,OAAO,CAACa,IAAI,IAAI,EAAE;UACxBC,QAAQ,EAAEd,OAAO,CAACc,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAEf,OAAO,CAACe,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAEhB,OAAO,CAACgB,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAE,EAAAY,iBAAA,GAAA7B,OAAO,CAACiB,QAAQ,cAAAY,iBAAA,uBAAhBA,iBAAA,CAAkBM,GAAG,KAAInC,OAAO,CAACiB,QAAQ,IAAI,EAAE;UACzDC,WAAW,EAAElB,OAAO,CAACkB,WAAW,IAAI,EAAE;UACtCC,QAAQ,EAAE;YACRC,SAAS,EAAE,EAAAU,iBAAA,GAAA9B,OAAO,CAACmB,QAAQ,cAAAW,iBAAA,uBAAhBA,iBAAA,CAAkBV,SAAS,KAAI,EAAE;YAC5CC,KAAK,EAAE,EAAAU,kBAAA,GAAA/B,OAAO,CAACmB,QAAQ,cAAAY,kBAAA,uBAAhBA,kBAAA,CAAkBV,KAAK,KAAI,EAAE;YACpCC,KAAK,EAAE,EAAAU,kBAAA,GAAAhC,OAAO,CAACmB,QAAQ,cAAAa,kBAAA,uBAAhBA,kBAAA,CAAkBV,KAAK,KAAI,EAAE;YACpCC,GAAG,EAAE,EAAAU,kBAAA,GAAAjC,OAAO,CAACmB,QAAQ,cAAAc,kBAAA,uBAAhBA,kBAAA,CAAkBV,GAAG,KAAI;UAChC,CAAC;UACDC,YAAY,EAAExB,OAAO,CAACwB,YAAY,IAAI,EAAE;UACxCC,eAAe,EAAEzB,OAAO,CAACyB,eAAe,IAAI,EAAE;UAC9CC,IAAI,EAAE,EAAAQ,aAAA,GAAAlC,OAAO,CAAC0B,IAAI,cAAAQ,aAAA,uBAAZA,aAAA,CAAcE,IAAI,CAAC,IAAI,CAAC,KAAI;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA5B,WAAW,CAAC;UACVC,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,EAAE;UACfC,QAAQ,EAAE;YACRC,SAAS,EAAE,EAAE;YACbC,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE,EAAE;YACTC,GAAG,EAAE;UACP,CAAC;UACDC,YAAY,EAAE,EAAE;UAChBC,eAAe,EAAE,EAAE;UACnBC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEE,OAAO,CAAC,CAAC;EAErB,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM5C,YAAY,CAAC6C,MAAM,CAAC;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;MAChEjC,YAAY,CAAC+B,QAAQ,CAACG,IAAI,CAACnC,SAAS,IAAI,EAAE,CAAC;IAC7C,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElC,IAAI;MAAEmC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCtC,WAAW,CAACuC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACrC,IAAI,GAAGmC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB;IACAP,OAAO,CAACQ,GAAG,CAAC,iBAAiB,EAAE3C,QAAQ,CAAC;IACxCR,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKuD,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjDxD,OAAA;MAAKuD,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBACxGxD,OAAA;QAAKuD,SAAS,EAAC,4DAA4D;QAACE,OAAO,EAAEtD;MAAQ;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpG7D,OAAA;QAAKuD,SAAS,EAAC,0JAA0J;QAAAC,QAAA,eACvKxD,OAAA;UAAKuD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxD,OAAA;YAAKuD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDxD,OAAA;cAAIuD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE7D,OAAA;cACEyD,OAAO,EAAEtD,OAAQ;cACjBoD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAE7CxD,OAAA,CAACN,CAAC;gBAAC6D,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7D,OAAA;YAAM8D,QAAQ,EAAEV,YAAa;YAACG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDxD,OAAA;cAAKuD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE+D,IAAI,EAAC,MAAM;kBACXjD,IAAI,EAAC,KAAK;kBACVmC,KAAK,EAAEtC,QAAQ,CAACE,GAAI;kBACpBmD,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACEc,IAAI,EAAC,UAAU;kBACfmC,KAAK,EAAEtC,QAAQ,CAACI,QAAS;kBACzBiD,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;kBAAAT,QAAA,gBAERxD,OAAA;oBAAQiD,KAAK,EAAC,EAAE;oBAAAO,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACxC9B,UAAU,CAACmC,GAAG,CAACnD,QAAQ,iBACtBf,OAAA;oBAAuBiD,KAAK,EAAElC,QAAS;oBAAAyC,QAAA,EAAEzC;kBAAQ,GAApCA,QAAQ;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAOuD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACE+D,IAAI,EAAC,MAAM;gBACXjD,IAAI,EAAC,MAAM;gBACXmC,KAAK,EAAEtC,QAAQ,CAACG,IAAK;gBACrBkD,QAAQ,EAAEjB,YAAa;gBACvBQ,SAAS,EAAC,oIAAoI;gBAC9IU,QAAQ;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAKuD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXrD,IAAI,EAAC,OAAO;kBACZmC,KAAK,EAAEtC,QAAQ,CAACK,KAAM;kBACtBgD,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXrD,IAAI,EAAC,MAAM;kBACXmC,KAAK,EAAEtC,QAAQ,CAACM,IAAK;kBACrB+C,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7D,OAAA;cAAKuD,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbjD,IAAI,EAAC,UAAU;kBACfmC,KAAK,EAAEtC,QAAQ,CAACO,QAAS;kBACzB8C,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAOuD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbjD,IAAI,EAAC,UAAU;kBACfmC,KAAK,EAAEtC,QAAQ,CAACQ,QAAS;kBACzB6C,QAAQ,EAAEjB,YAAa;kBACvBQ,SAAS,EAAC,oIAAoI;kBAC9IU,QAAQ;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAOuD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEc,IAAI,EAAC,UAAU;gBACfmC,KAAK,EAAEtC,QAAQ,CAACU,QAAS;gBACzB2C,QAAQ,EAAEjB,YAAa;gBACvBQ,SAAS,EAAC,oIAAoI;gBAC9IU,QAAQ;gBAAAT,QAAA,gBAERxD,OAAA;kBAAQiD,KAAK,EAAC,EAAE;kBAAAO,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACxCpD,SAAS,CAACyD,GAAG,CAAC7C,QAAQ,iBACrBrB,OAAA;kBAAuBiD,KAAK,EAAE5B,QAAS;kBAAAmC,QAAA,EAAEnC;gBAAQ,GAApCA,QAAQ;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAOuD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEc,IAAI,EAAC,aAAa;gBAClBmC,KAAK,EAAEtC,QAAQ,CAACW,WAAY;gBAC5B0C,QAAQ,EAAEjB,YAAa;gBACvBqB,IAAI,EAAE,CAAE;gBACRb,SAAS,EAAC;cAAoI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/I,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7D,OAAA;cAAKuD,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9CxD,OAAA;gBACE+D,IAAI,EAAC,QAAQ;gBACbN,OAAO,EAAEtD,OAAQ;gBACjBoD,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA;gBACE+D,IAAI,EAAC,QAAQ;gBACbR,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CAnSIL,iBAAiB;AAAAoE,EAAA,GAAjBpE,iBAAiB;AAqSvB,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}