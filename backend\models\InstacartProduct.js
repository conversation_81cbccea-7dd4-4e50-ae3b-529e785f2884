const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  product_id: {
    type: Number,
    required: true,
    unique: true
  },
  product_name: {
    type: String,
    required: true,
    trim: true
  },
  aisle_id: {
    type: Number,
    required: true,
    ref: 'InstacartAisle'
  },
  department_id: {
    type: Number,
    required: true,
    ref: 'InstacartDepartment'
  }
}, {
  timestamps: true,
  collection: 'instacart_products'
});

// Indexes for better query performance
productSchema.index({ product_id: 1 });
productSchema.index({ product_name: 1 });
productSchema.index({ aisle_id: 1 });
productSchema.index({ department_id: 1 });
productSchema.index({ product_name: 'text' }); // Text search index

module.exports = mongoose.model('InstacartProduct', productSchema);
