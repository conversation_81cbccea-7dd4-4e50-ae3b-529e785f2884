[{"F:\\SIC BigData\\InventoryManagement\\src\\index.js": "1", "F:\\SIC BigData\\InventoryManagement\\src\\App.js": "2", "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js": "3", "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js": "4", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js": "5", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js": "6", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js": "7", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js": "8", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js": "9", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js": "10", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js": "11", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js": "12", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js": "13", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js": "14", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js": "15", "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js": "16", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js": "17", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js": "18", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js": "19", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js": "20", "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js": "21", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js": "22", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js": "23", "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js": "24"}, {"size": 599, "mtime": 1753275041292, "results": "25", "hashOfConfig": "26"}, {"size": 2322, "mtime": 1754392108532, "results": "27", "hashOfConfig": "26"}, {"size": 1965, "mtime": 1753281864341, "results": "28", "hashOfConfig": "26"}, {"size": 7097, "mtime": 1753281822017, "results": "29", "hashOfConfig": "26"}, {"size": 2973, "mtime": 1753275118699, "results": "30", "hashOfConfig": "26"}, {"size": 743, "mtime": 1753275063328, "results": "31", "hashOfConfig": "26"}, {"size": 8692, "mtime": 1754392694906, "results": "32", "hashOfConfig": "26"}, {"size": 7204, "mtime": 1753281850508, "results": "33", "hashOfConfig": "26"}, {"size": 14941, "mtime": 1754393307713, "results": "34", "hashOfConfig": "26"}, {"size": 12727, "mtime": 1754392949328, "results": "35", "hashOfConfig": "26"}, {"size": 16262, "mtime": 1754394995292, "results": "36", "hashOfConfig": "26"}, {"size": 3412, "mtime": 1753275079903, "results": "37", "hashOfConfig": "26"}, {"size": 4462, "mtime": 1753281912430, "results": "38", "hashOfConfig": "26"}, {"size": 1226, "mtime": 1753275145224, "results": "39", "hashOfConfig": "26"}, {"size": 1194, "mtime": 1753275129694, "results": "40", "hashOfConfig": "26"}, {"size": 5645, "mtime": 1754392585222, "results": "41", "hashOfConfig": "26"}, {"size": 4144, "mtime": 1753275178958, "results": "42", "hashOfConfig": "26"}, {"size": 2047, "mtime": 1753275159082, "results": "43", "hashOfConfig": "26"}, {"size": 19536, "mtime": 1754392290135, "results": "44", "hashOfConfig": "26"}, {"size": 6943, "mtime": 1754392877858, "results": "45", "hashOfConfig": "26"}, {"size": 23105, "mtime": 1754392082047, "results": "46", "hashOfConfig": "26"}, {"size": 14000, "mtime": 1754392543260, "results": "47", "hashOfConfig": "26"}, {"size": 20157, "mtime": 1754392373357, "results": "48", "hashOfConfig": "26"}, {"size": 24597, "mtime": 1754392484422, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ar5hf7", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\SIC BigData\\InventoryManagement\\src\\index.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\App.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\ProtectedRoute.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\contexts\\AuthContext.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Dashboard.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Layout.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Inventory.js", ["122"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Login.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\SalesOrders.js", ["123"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Reports.js", ["124"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Suppliers.js", ["125"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Sidebar.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Layout\\Header.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\InventoryChart.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\StatsCard.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\services\\api.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\RecentOrders.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Dashboard\\LowStockAlert.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\AddInventoryModal.js", [], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Inventory\\InventoryTable.js", ["126", "127", "128"], [], "F:\\SIC BigData\\InventoryManagement\\src\\pages\\Settings.js", ["129", "130", "131", "132", "133", "134"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Reports\\ExportModal.js", ["135", "136", "137"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Suppliers\\AddSupplierModal.js", ["138"], [], "F:\\SIC BigData\\InventoryManagement\\src\\components\\Orders\\AddOrderModal.js", [], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 2, "column": 42, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 46}, {"ruleId": "139", "severity": 1, "message": "143", "line": 7, "column": 7, "nodeType": "141", "messageId": "142", "endLine": 7, "endColumn": 22}, {"ruleId": "139", "severity": 1, "message": "144", "line": 2, "column": 20, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 28}, {"ruleId": "139", "severity": 1, "message": "145", "line": 7, "column": 7, "nodeType": "141", "messageId": "142", "endLine": 7, "endColumn": 20}, {"ruleId": "139", "severity": 1, "message": "146", "line": 2, "column": 24, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 27}, {"ruleId": "139", "severity": 1, "message": "147", "line": 9, "column": 10, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 23}, {"ruleId": "139", "severity": 1, "message": "148", "line": 9, "column": 25, "nodeType": "141", "messageId": "142", "endLine": 9, "endColumn": 41}, {"ruleId": "139", "severity": 1, "message": "149", "line": 7, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 7, "endColumn": 7}, {"ruleId": "139", "severity": 1, "message": "150", "line": 8, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 8, "endColumn": 8}, {"ruleId": "139", "severity": 1, "message": "151", "line": 12, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 12, "endColumn": 8}, {"ruleId": "139", "severity": 1, "message": "152", "line": 13, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 13, "endColumn": 4}, {"ruleId": "139", "severity": 1, "message": "153", "line": 15, "column": 19, "nodeType": "141", "messageId": "142", "endLine": 15, "endColumn": 27}, {"ruleId": "154", "severity": 1, "message": "155", "line": 63, "column": 6, "nodeType": "156", "endLine": 63, "endColumn": 8, "suggestions": "157"}, {"ruleId": "139", "severity": 1, "message": "158", "line": 2, "column": 60, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 66}, {"ruleId": "139", "severity": 1, "message": "159", "line": 77, "column": 9, "nodeType": "141", "messageId": "142", "endLine": 77, "endColumn": 26}, {"ruleId": "139", "severity": 1, "message": "160", "line": 143, "column": 9, "nodeType": "141", "messageId": "142", "endLine": 143, "endColumn": 24}, {"ruleId": "139", "severity": 1, "message": "161", "line": 55, "column": 9, "nodeType": "141", "messageId": "142", "endLine": 55, "endColumn": 24}, "no-unused-vars", "'Edit' is defined but never used.", "Identifier", "unusedVar", "'salesOrdersData' is assigned a value but never used.", "'Calendar' is defined but never used.", "'suppliersData' is assigned a value but never used.", "'Eye' is defined but never used.", "'selectedItems' is assigned a value but never used.", "'setSelectedItems' is assigned a value but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'Check' is defined but never used.", "'X' is defined but never used.", "'usersAPI' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", "ArrayExpression", ["162"], "'Filter' is defined but never used.", "'handleFieldToggle' is assigned a value but never used.", "'getFieldOptions' is assigned a value but never used.", "'currencyOptions' is assigned a value but never used.", {"desc": "163", "fix": "164"}, "Update the dependencies array to be: [fetchUserProfile]", {"range": "165", "text": "166"}, [1331, 1333], "[fetchUserProfile]"]