{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\SalesOrders.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Eye, Edit, Trash2, Loader } from 'lucide-react';\nimport AddOrderModal from '../components/Orders/AddOrderModal';\nimport { ordersAPI } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst salesOrdersData = [{\n  id: 'ORD-001',\n  customer: '<PERSON>',\n  email: '<EMAIL>',\n  date: '2024-01-15',\n  status: 'Completed',\n  total: 1234.56,\n  items: [{\n    name: 'iPhone 14 Pro',\n    quantity: 1,\n    price: 1099.99\n  }, {\n    name: 'Phone Case',\n    quantity: 1,\n    price: 29.99\n  }],\n  shippingAddress: '123 Main St, New York, NY 10001'\n}, {\n  id: 'ORD-002',\n  customer: '<PERSON>',\n  email: '<EMAIL>',\n  date: '2024-01-15',\n  status: 'Processing',\n  total: 856.90,\n  items: [{\n    name: 'Samsung Galaxy S23',\n    quantity: 1,\n    price: 799.99\n  }, {\n    name: 'Wireless Charger',\n    quantity: 1,\n    price: 49.99\n  }],\n  shippingAddress: '456 Oak Ave, Los Angeles, CA 90210'\n}, {\n  id: 'ORD-003',\n  customer: 'Mike Davis',\n  email: '<EMAIL>',\n  date: '2024-01-14',\n  status: 'Shipped',\n  total: 2145.78,\n  items: [{\n    name: 'MacBook Pro M2',\n    quantity: 1,\n    price: 1999.99\n  }, {\n    name: 'USB-C Hub',\n    quantity: 1,\n    price: 89.99\n  }],\n  shippingAddress: '789 Pine St, Chicago, IL 60601'\n}, {\n  id: 'ORD-004',\n  customer: 'Emily Brown',\n  email: '<EMAIL>',\n  date: '2024-01-14',\n  status: 'Pending',\n  total: 567.23,\n  items: [{\n    name: 'Nike Air Max',\n    quantity: 2,\n    price: 150.00\n  }, {\n    name: 'Sports Socks',\n    quantity: 3,\n    price: 15.99\n  }],\n  shippingAddress: '321 Elm St, Miami, FL 33101'\n}];\nconst SalesOrders = () => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingOrder, setEditingOrder] = useState(null);\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const statuses = [{\n    value: 'all',\n    label: 'All Statuses'\n  }, {\n    value: 'pending',\n    label: 'Pending'\n  }, {\n    value: 'processing',\n    label: 'Processing'\n  }, {\n    value: 'shipped',\n    label: 'Shipped'\n  }, {\n    value: 'completed',\n    label: 'Completed'\n  }, {\n    value: 'cancelled',\n    label: 'Cancelled'\n  }];\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800';\n      case 'shipped':\n        return 'bg-purple-100 text-purple-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const filteredOrders = salesOrdersData.filter(order => {\n    const matchesSearch = order.customer.toLowerCase().includes(searchTerm.toLowerCase()) || order.id.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || order.status.toLowerCase() === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Sales Orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage customer orders and track fulfillment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), \"New Order\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1 max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search orders...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: statusFilter,\n              onChange: e => setStatusFilter(e.target.value),\n              className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n              children: statuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: status.value,\n                children: status.label\n              }, status.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"Export\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Order ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Customer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Items\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredOrders.map(order => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: order.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: order.customer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: order.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: order.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`,\n                  children: order.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [order.items.length, \" items\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: [\"$\", order.total.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    onClick: () => setSelectedOrder(order),\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-green-600 hover:text-green-900\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), filteredOrders.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"No orders found matching your criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), selectedOrder && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-50 overflow-y-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n          onClick: () => setSelectedOrder(null)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: [\"Order Details - \", selectedOrder.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedOrder(null),\n                className: \"text-gray-400 hover:text-gray-600\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"Customer Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: selectedOrder.customer\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: selectedOrder.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"Shipping Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: selectedOrder.shippingAddress\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"Items\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), selectedOrder.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [item.name, \" (x\", item.quantity, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"$\", item.price.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-t pt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Total\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"$\", selectedOrder.total.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(SalesOrders, \"nZWXp2sthAZLlPUEA2AWtMz7h5s=\");\n_c = SalesOrders;\nexport default SalesOrders;\nvar _c;\n$RefreshReg$(_c, \"SalesOrders\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plus", "Search", "Filter", "Download", "Eye", "Edit", "Trash2", "Loader", "AddOrderModal", "ordersAPI", "toast", "jsxDEV", "_jsxDEV", "salesOrdersData", "id", "customer", "email", "date", "status", "total", "items", "name", "quantity", "price", "shippingAddress", "SalesOrders", "_s", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedOrder", "showAddModal", "setShowAddModal", "editingOrder", "setEditingOrder", "orders", "setOrders", "loading", "setLoading", "statuses", "value", "label", "getStatusColor", "toLowerCase", "filteredOrders", "filter", "order", "matchesSearch", "includes", "matchesStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "onChange", "e", "target", "map", "length", "toFixed", "onClick", "item", "index", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/SalesOrders.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Eye, Edit, Trash2, Loader } from 'lucide-react';\nimport AddOrderModal from '../components/Orders/AddOrderModal';\nimport { ordersAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\nconst salesOrdersData = [\n  {\n    id: 'ORD-001',\n    customer: '<PERSON>',\n    email: '<EMAIL>',\n    date: '2024-01-15',\n    status: 'Completed',\n    total: 1234.56,\n    items: [\n      { name: 'iPhone 14 Pro', quantity: 1, price: 1099.99 },\n      { name: 'Phone Case', quantity: 1, price: 29.99 }\n    ],\n    shippingAddress: '123 Main St, New York, NY 10001'\n  },\n  {\n    id: 'ORD-002',\n    customer: '<PERSON>',\n    email: '<EMAIL>',\n    date: '2024-01-15',\n    status: 'Processing',\n    total: 856.90,\n    items: [\n      { name: 'Samsung Galaxy S23', quantity: 1, price: 799.99 },\n      { name: 'Wireless Charger', quantity: 1, price: 49.99 }\n    ],\n    shippingAddress: '456 Oak Ave, Los Angeles, CA 90210'\n  },\n  {\n    id: 'ORD-003',\n    customer: '<PERSON>',\n    email: '<EMAIL>',\n    date: '2024-01-14',\n    status: 'Shipped',\n    total: 2145.78,\n    items: [\n      { name: 'MacBook Pro M2', quantity: 1, price: 1999.99 },\n      { name: 'USB-C Hub', quantity: 1, price: 89.99 }\n    ],\n    shippingAddress: '789 Pine St, Chicago, IL 60601'\n  },\n  {\n    id: 'ORD-004',\n    customer: 'Emily Brown',\n    email: '<EMAIL>',\n    date: '2024-01-14',\n    status: 'Pending',\n    total: 567.23,\n    items: [\n      { name: 'Nike Air Max', quantity: 2, price: 150.00 },\n      { name: 'Sports Socks', quantity: 3, price: 15.99 }\n    ],\n    shippingAddress: '321 Elm St, Miami, FL 33101'\n  }\n];\n\nconst SalesOrders = () => {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [selectedOrder, setSelectedOrder] = useState(null);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingOrder, setEditingOrder] = useState(null);\n  const [orders, setOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  const statuses = [\n    { value: 'all', label: 'All Statuses' },\n    { value: 'pending', label: 'Pending' },\n    { value: 'processing', label: 'Processing' },\n    { value: 'shipped', label: 'Shipped' },\n    { value: 'completed', label: 'Completed' },\n    { value: 'cancelled', label: 'Cancelled' }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'processing':\n        return 'bg-blue-100 text-blue-800';\n      case 'shipped':\n        return 'bg-purple-100 text-purple-800';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const filteredOrders = salesOrdersData.filter(order => {\n    const matchesSearch = order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         order.id.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || \n                         order.status.toLowerCase() === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Sales Orders</h1>\n          <p className=\"text-gray-600\">Manage customer orders and track fulfillment</p>\n        </div>\n        <button className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\">\n          <Plus className=\"h-5 w-5 mr-2\" />\n          New Order\n        </button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\">\n          {/* Search */}\n          <div className=\"relative flex-1 max-w-md\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search orders...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Status Filter */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n              >\n                {statuses.map((status) => (\n                  <option key={status.value} value={status.value}>\n                    {status.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Orders Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Order ID\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Customer\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Date\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Items\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Total\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredOrders.map((order) => (\n                <tr key={order.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    {order.id}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{order.customer}</div>\n                      <div className=\"text-sm text-gray-500\">{order.email}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {order.date}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>\n                      {order.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {order.items.length} items\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    ${order.total.toFixed(2)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    <div className=\"flex space-x-2\">\n                      <button \n                        className=\"text-blue-600 hover:text-blue-900\"\n                        onClick={() => setSelectedOrder(order)}\n                      >\n                        <Eye className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-900\">\n                        <Edit className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-red-600 hover:text-red-900\">\n                        <Trash2 className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {filteredOrders.length === 0 && (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-500\">No orders found matching your criteria.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Order Details Modal */}\n      {selectedOrder && (\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n          <div className=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n            <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={() => setSelectedOrder(null)}></div>\n            <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n              <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-medium text-gray-900\">Order Details - {selectedOrder.id}</h3>\n                  <button onClick={() => setSelectedOrder(null)} className=\"text-gray-400 hover:text-gray-600\">\n                    ×\n                  </button>\n                </div>\n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Customer Information</h4>\n                    <p className=\"text-sm text-gray-600\">{selectedOrder.customer}</p>\n                    <p className=\"text-sm text-gray-600\">{selectedOrder.email}</p>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Shipping Address</h4>\n                    <p className=\"text-sm text-gray-600\">{selectedOrder.shippingAddress}</p>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Items</h4>\n                    {selectedOrder.items.map((item, index) => (\n                      <div key={index} className=\"flex justify-between text-sm\">\n                        <span>{item.name} (x{item.quantity})</span>\n                        <span>${item.price.toFixed(2)}</span>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"border-t pt-2\">\n                    <div className=\"flex justify-between font-medium\">\n                      <span>Total</span>\n                      <span>${selectedOrder.total.toFixed(2)}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SalesOrders;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,QAAQ,cAAc;AACxF,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAG,CACtB;EACEC,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,eAAe;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACtD;IAAEF,IAAI,EAAE,YAAY;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,CAClD;EACDC,eAAe,EAAE;AACnB,CAAC,EACD;EACEV,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,YAAY;EACpBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,oBAAoB;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC1D;IAAEF,IAAI,EAAE,kBAAkB;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,CACxD;EACDC,eAAe,EAAE;AACnB,CAAC,EACD;EACEV,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACvD;IAAEF,IAAI,EAAE,WAAW;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,CACjD;EACDC,eAAe,EAAE;AACnB,CAAC,EACD;EACEV,EAAE,EAAE,SAAS;EACbC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE,uBAAuB;EAC9BC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAE,CACL;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EACpD;IAAEF,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,CACpD;EACDC,eAAe,EAAE;AACnB,CAAC,CACF;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM2C,QAAQ,GAAG,CACf;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC5C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,CAC3C;EAED,MAAMC,cAAc,GAAI1B,MAAM,IAAK;IACjC,QAAQA,MAAM,CAAC2B,WAAW,CAAC,CAAC;MAC1B,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,YAAY;QACf,OAAO,2BAA2B;MACpC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMC,cAAc,GAAGjC,eAAe,CAACkC,MAAM,CAACC,KAAK,IAAI;IACrD,MAAMC,aAAa,GAAGD,KAAK,CAACjC,QAAQ,CAAC8B,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACvB,UAAU,CAACkB,WAAW,CAAC,CAAC,CAAC,IAChEG,KAAK,CAAClC,EAAE,CAAC+B,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACvB,UAAU,CAACkB,WAAW,CAAC,CAAC,CAAC;IAC9E,MAAMM,aAAa,GAAGtB,YAAY,KAAK,KAAK,IACvBmB,KAAK,CAAC9B,MAAM,CAAC2B,WAAW,CAAC,CAAC,KAAKhB,YAAY;IAChE,OAAOoB,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;EAEF,oBACEvC,OAAA;IAAKwC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzC,OAAA;MAAKwC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzC,OAAA;QAAAyC,QAAA,gBACEzC,OAAA;UAAIwC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClE7C,OAAA;UAAGwC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eACN7C,OAAA;QAAQwC,SAAS,EAAC,gHAAgH;QAAAC,QAAA,gBAChIzC,OAAA,CAACZ,IAAI;UAACoD,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzC,OAAA;QAAKwC,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAE/GzC,OAAA;UAAKwC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCzC,OAAA;YAAKwC,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFzC,OAAA,CAACX,MAAM;cAACmD,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN7C,OAAA;YACE8C,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BjB,KAAK,EAAEf,UAAW;YAClBiC,QAAQ,EAAGC,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;YAC/CU,SAAS,EAAC;UAAmN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7C,OAAA;UAAKwC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzC,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzC,OAAA,CAACV,MAAM;cAACkD,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C7C,OAAA;cACE8B,KAAK,EAAEb,YAAa;cACpB+B,QAAQ,EAAGC,CAAC,IAAK/B,eAAe,CAAC+B,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;cACjDU,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EAE/IZ,QAAQ,CAACsB,GAAG,CAAE7C,MAAM,iBACnBN,OAAA;gBAA2B8B,KAAK,EAAExB,MAAM,CAACwB,KAAM;gBAAAW,QAAA,EAC5CnC,MAAM,CAACyB;cAAK,GADFzB,MAAM,CAACwB,KAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7C,OAAA;YAAQwC,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAChJzC,OAAA,CAACT,QAAQ;cAACiD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnFzC,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BzC,OAAA;UAAOwC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDzC,OAAA;YAAOwC,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BzC,OAAA;cAAAyC,QAAA,gBACEzC,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR7C,OAAA;YAAOwC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDP,cAAc,CAACiB,GAAG,CAAEf,KAAK,iBACxBpC,OAAA;cAAmBwC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7CzC,OAAA;gBAAIwC,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1EL,KAAK,CAAClC;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCzC,OAAA;kBAAAyC,QAAA,gBACEzC,OAAA;oBAAKwC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEL,KAAK,CAACjC;kBAAQ;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzE7C,OAAA;oBAAKwC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEL,KAAK,CAAChC;kBAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DL,KAAK,CAAC/B;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzCzC,OAAA;kBAAMwC,SAAS,EAAE,4DAA4DR,cAAc,CAACI,KAAK,CAAC9B,MAAM,CAAC,EAAG;kBAAAmC,QAAA,EACzGL,KAAK,CAAC9B;gBAAM;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAC9DL,KAAK,CAAC5B,KAAK,CAAC4C,MAAM,EAAC,QACtB;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,GAAC,GAC3E,EAACL,KAAK,CAAC7B,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eAC/DzC,OAAA;kBAAKwC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BzC,OAAA;oBACEwC,SAAS,EAAC,mCAAmC;oBAC7Cc,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACgB,KAAK,CAAE;oBAAAK,QAAA,eAEvCzC,OAAA,CAACR,GAAG;sBAACgD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACT7C,OAAA;oBAAQwC,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eACrDzC,OAAA,CAACP,IAAI;sBAAC+C,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACT7C,OAAA;oBAAQwC,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,eACjDzC,OAAA,CAACN,MAAM;sBAAC8C,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAvCET,KAAK,CAAClC,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwCb,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELX,cAAc,CAACkB,MAAM,KAAK,CAAC,iBAC1BpD,OAAA;QAAKwC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BzC,OAAA;UAAGwC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL1B,aAAa,iBACZnB,OAAA;MAAKwC,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eACjDzC,OAAA;QAAKwC,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGzC,OAAA;UAAKwC,SAAS,EAAC,4DAA4D;UAACc,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC,IAAI;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzH7C,OAAA;UAAKwC,SAAS,EAAC,0JAA0J;UAAAC,QAAA,eACvKzC,OAAA;YAAKwC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzC,OAAA;cAAKwC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDzC,OAAA;gBAAIwC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,kBAAgB,EAACtB,aAAa,CAACjB,EAAE;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzF7C,OAAA;gBAAQsD,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC,IAAI,CAAE;gBAACoB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAE7F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzC,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAIwC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnE7C,OAAA;kBAAGwC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEtB,aAAa,CAAChB;gBAAQ;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjE7C,OAAA;kBAAGwC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEtB,aAAa,CAACf;gBAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACN7C,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAIwC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/D7C,OAAA;kBAAGwC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEtB,aAAa,CAACP;gBAAe;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACN7C,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBAAIwC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACnD1B,aAAa,CAACX,KAAK,CAAC2C,GAAG,CAAC,CAACI,IAAI,EAAEC,KAAK,kBACnCxD,OAAA;kBAAiBwC,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBACvDzC,OAAA;oBAAAyC,QAAA,GAAOc,IAAI,CAAC9C,IAAI,EAAC,KAAG,EAAC8C,IAAI,CAAC7C,QAAQ,EAAC,GAAC;kBAAA;oBAAAgC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3C7C,OAAA;oBAAAyC,QAAA,GAAM,GAAC,EAACc,IAAI,CAAC5C,KAAK,CAAC0C,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAF7BW,KAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7C,OAAA;gBAAKwC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BzC,OAAA;kBAAKwC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CzC,OAAA;oBAAAyC,QAAA,EAAM;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClB7C,OAAA;oBAAAyC,QAAA,GAAM,GAAC,EAACtB,aAAa,CAACZ,KAAK,CAAC8C,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/B,EAAA,CArOID,WAAW;AAAA4C,EAAA,GAAX5C,WAAW;AAuOjB,eAAeA,WAAW;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}