{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Inventory\\\\InventoryTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Edit, Trash2, Eye, <PERSON><PERSON>Triangle, Loader } from 'lucide-react';\n\n// Mock data - in real app this would come from API\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst inventoryData = [{\n  id: 1,\n  sku: 'IPH14P-256',\n  name: 'iPhone 14 Pro 256GB',\n  category: 'Electronics',\n  price: 1099.99,\n  cost: 850.00,\n  quantity: 45,\n  minStock: 20,\n  supplier: 'Apple Inc.',\n  lastUpdated: '2024-01-15'\n}, {\n  id: 2,\n  sku: 'NAM-001',\n  name: 'Nike Air Max 270',\n  category: 'Footwear',\n  price: 150.00,\n  cost: 90.00,\n  quantity: 3,\n  minStock: 15,\n  supplier: 'Nike',\n  lastUpdated: '2024-01-14'\n}, {\n  id: 3,\n  sku: 'SGS23-128',\n  name: 'Samsung Galaxy S23 128GB',\n  category: 'Electronics',\n  price: 899.99,\n  cost: 650.00,\n  quantity: 28,\n  minStock: 25,\n  supplier: 'Samsung',\n  lastUpdated: '2024-01-13'\n}, {\n  id: 4,\n  sku: 'MBP-M2-512',\n  name: 'MacBook Pro M2 512GB',\n  category: 'Electronics',\n  price: 1999.99,\n  cost: 1500.00,\n  quantity: 12,\n  minStock: 10,\n  supplier: 'Apple Inc.',\n  lastUpdated: '2024-01-12'\n}, {\n  id: 5,\n  sku: 'AUB-22',\n  name: 'Adidas Ultraboost 22',\n  category: 'Footwear',\n  price: 180.00,\n  cost: 110.00,\n  quantity: 6,\n  minStock: 20,\n  supplier: 'Adidas',\n  lastUpdated: '2024-01-11'\n}];\nconst InventoryTable = ({\n  products = [],\n  loading = false,\n  onEdit,\n  onDelete,\n  onRefresh\n}) => {\n  _s();\n  const [sortField, setSortField] = useState('name');\n  const [sortDirection, setSortDirection] = useState('asc');\n  const [selectedItems, setSelectedItems] = useState([]);\n\n  // Sort logic\n  const sortedData = [...products].sort((a, b) => {\n    if (sortDirection === 'asc') {\n      return a[sortField] > b[sortField] ? 1 : -1;\n    } else {\n      return a[sortField] < b[sortField] ? 1 : -1;\n    }\n  });\n  const handleSort = field => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n  const getStockStatus = (quantity, minStock) => {\n    if (quantity === 0) {\n      return {\n        status: 'Out of Stock',\n        color: 'text-red-600 bg-red-100'\n      };\n    } else if (quantity <= minStock) {\n      return {\n        status: 'Low Stock',\n        color: 'text-yellow-600 bg-yellow-100'\n      };\n    } else {\n      return {\n        status: 'In Stock',\n        color: 'text-green-600 bg-green-100'\n      };\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"overflow-x-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"min-w-full divide-y divide-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        className: \"bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('sku'),\n            children: \"SKU\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('name'),\n            children: \"Product Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('category'),\n            children: \"Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('price'),\n            children: \"Price\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('quantity'),\n            children: \"Stock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n            onClick: () => handleSort('supplier'),\n            children: \"Supplier\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        className: \"bg-white divide-y divide-gray-200\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: /*#__PURE__*/_jsxDEV(\"td\", {\n            colSpan: \"8\",\n            className: \"px-6 py-12 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\",\n              children: [/*#__PURE__*/_jsxDEV(Loader, {\n                className: \"h-6 w-6 animate-spin text-gray-400 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-500\",\n                children: \"Loading products...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this) : sortedData.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: /*#__PURE__*/_jsxDEV(\"td\", {\n            colSpan: \"8\",\n            className: \"px-6 py-12 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-500\",\n              children: \"No products found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this) : sortedData.map(item => {\n          const stockStatus = getStockStatus(item.quantity, item.minStock);\n          return /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n              children: item.sku\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this), item.quantity <= item.minStock && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center text-xs text-red-600\",\n                    children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this), \"Low Stock Alert\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: item.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: [\"$\", item.price.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: item.quantity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`,\n                children: stockStatus.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: item.supplier\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onEdit && onEdit(item),\n                  className: \"text-blue-600 hover:text-blue-900\",\n                  title: \"Edit Product\",\n                  children: /*#__PURE__*/_jsxDEV(Edit, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => onDelete && onDelete(item._id || item.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  title: \"Delete Product\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, item._id || item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), sortedData.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No products found matching your criteria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(InventoryTable, \"COFRsdUiJANnZ0AY9SzLMBfMoZ4=\");\n_c = InventoryTable;\nexport default InventoryTable;\nvar _c;\n$RefreshReg$(_c, \"InventoryTable\");", "map": {"version": 3, "names": ["React", "useState", "Edit", "Trash2", "Eye", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader", "jsxDEV", "_jsxDEV", "inventoryData", "id", "sku", "name", "category", "price", "cost", "quantity", "minStock", "supplier", "lastUpdated", "InventoryTable", "products", "loading", "onEdit", "onDelete", "onRefresh", "_s", "sortField", "setSortField", "sortDirection", "setSortDirection", "selectedItems", "setSelectedItems", "sortedData", "sort", "a", "b", "handleSort", "field", "getStockStatus", "status", "color", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "colSpan", "length", "map", "item", "stockStatus", "toFixed", "title", "_id", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Inventory/InventoryTable.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Edit, Trash2, <PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Loader } from 'lucide-react';\n\n// Mock data - in real app this would come from API\nconst inventoryData = [\n  {\n    id: 1,\n    sku: 'IPH14P-256',\n    name: 'iPhone 14 Pro 256GB',\n    category: 'Electronics',\n    price: 1099.99,\n    cost: 850.00,\n    quantity: 45,\n    minStock: 20,\n    supplier: 'Apple Inc.',\n    lastUpdated: '2024-01-15'\n  },\n  {\n    id: 2,\n    sku: 'NAM-001',\n    name: 'Nike Air Max 270',\n    category: 'Footwear',\n    price: 150.00,\n    cost: 90.00,\n    quantity: 3,\n    minStock: 15,\n    supplier: 'Nike',\n    lastUpdated: '2024-01-14'\n  },\n  {\n    id: 3,\n    sku: 'SGS23-128',\n    name: 'Samsung Galaxy S23 128GB',\n    category: 'Electronics',\n    price: 899.99,\n    cost: 650.00,\n    quantity: 28,\n    minStock: 25,\n    supplier: 'Samsung',\n    lastUpdated: '2024-01-13'\n  },\n  {\n    id: 4,\n    sku: 'MBP-M2-512',\n    name: 'MacBook Pro M2 512GB',\n    category: 'Electronics',\n    price: 1999.99,\n    cost: 1500.00,\n    quantity: 12,\n    minStock: 10,\n    supplier: 'Apple Inc.',\n    lastUpdated: '2024-01-12'\n  },\n  {\n    id: 5,\n    sku: 'AUB-22',\n    name: 'Adidas Ultraboost 22',\n    category: 'Footwear',\n    price: 180.00,\n    cost: 110.00,\n    quantity: 6,\n    minStock: 20,\n    supplier: 'Adidas',\n    lastUpdated: '2024-01-11'\n  }\n];\n\nconst InventoryTable = ({ products = [], loading = false, onEdit, onDelete, onRefresh }) => {\n  const [sortField, setSortField] = useState('name');\n  const [sortDirection, setSortDirection] = useState('asc');\n  const [selectedItems, setSelectedItems] = useState([]);\n\n  // Sort logic\n  const sortedData = [...products].sort((a, b) => {\n    if (sortDirection === 'asc') {\n      return a[sortField] > b[sortField] ? 1 : -1;\n    } else {\n      return a[sortField] < b[sortField] ? 1 : -1;\n    }\n  });\n\n  const handleSort = (field) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortDirection('asc');\n    }\n  };\n\n  const getStockStatus = (quantity, minStock) => {\n    if (quantity === 0) {\n      return { status: 'Out of Stock', color: 'text-red-600 bg-red-100' };\n    } else if (quantity <= minStock) {\n      return { status: 'Low Stock', color: 'text-yellow-600 bg-yellow-100' };\n    } else {\n      return { status: 'In Stock', color: 'text-green-600 bg-green-100' };\n    }\n  };\n\n  return (\n    <div className=\"overflow-x-auto\">\n      <table className=\"min-w-full divide-y divide-gray-200\">\n        <thead className=\"bg-gray-50\">\n          <tr>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('sku')}\n            >\n              SKU\n            </th>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('name')}\n            >\n              Product Name\n            </th>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('category')}\n            >\n              Category\n            </th>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('price')}\n            >\n              Price\n            </th>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('quantity')}\n            >\n              Stock\n            </th>\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n              Status\n            </th>\n            <th \n              className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n              onClick={() => handleSort('supplier')}\n            >\n              Supplier\n            </th>\n            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n              Actions\n            </th>\n          </tr>\n        </thead>\n        <tbody className=\"bg-white divide-y divide-gray-200\">\n          {loading ? (\n            <tr>\n              <td colSpan=\"8\" className=\"px-6 py-12 text-center\">\n                <div className=\"flex items-center justify-center\">\n                  <Loader className=\"h-6 w-6 animate-spin text-gray-400 mr-2\" />\n                  <span className=\"text-gray-500\">Loading products...</span>\n                </div>\n              </td>\n            </tr>\n          ) : sortedData.length === 0 ? (\n            <tr>\n              <td colSpan=\"8\" className=\"px-6 py-12 text-center\">\n                <div className=\"text-gray-500\">No products found</div>\n              </td>\n            </tr>\n          ) : (\n            sortedData.map((item) => {\n              const stockStatus = getStockStatus(item.quantity, item.minStock);\n              return (\n                <tr key={item._id || item.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                  {item.sku}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{item.name}</div>\n                      {item.quantity <= item.minStock && (\n                        <div className=\"flex items-center text-xs text-red-600\">\n                          <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                          Low Stock Alert\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {item.category}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  ${item.price.toFixed(2)}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {item.quantity}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>\n                    {stockStatus.status}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  {item.supplier}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => onEdit && onEdit(item)}\n                      className=\"text-blue-600 hover:text-blue-900\"\n                      title=\"Edit Product\"\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </button>\n                    <button\n                      onClick={() => onDelete && onDelete(item._id || item.id)}\n                      className=\"text-red-600 hover:text-red-900\"\n                      title=\"Delete Product\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            );\n          })\n          )}\n        </tbody>\n      </table>\n      \n      {sortedData.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">No products found matching your criteria.</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default InventoryTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,aAAa,EAAEC,MAAM,QAAQ,cAAc;;AAEvE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAG,CACpB;EACEC,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,YAAY;EACjBC,IAAI,EAAE,qBAAqB;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE;AACf,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,MAAM;EAChBC,WAAW,EAAE;AACf,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,WAAW;EAChBC,IAAI,EAAE,0BAA0B;EAChCC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE;AACf,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,YAAY;EACjBC,IAAI,EAAE,sBAAsB;EAC5BC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE;AACf,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,QAAQ;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,EAAE;EACZC,QAAQ,EAAE,QAAQ;EAClBC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ,GAAG,EAAE;EAAEC,OAAO,GAAG,KAAK;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC1F,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMgC,UAAU,GAAG,CAAC,GAAGZ,QAAQ,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC9C,IAAIP,aAAa,KAAK,KAAK,EAAE;MAC3B,OAAOM,CAAC,CAACR,SAAS,CAAC,GAAGS,CAAC,CAACT,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOQ,CAAC,CAACR,SAAS,CAAC,GAAGS,CAAC,CAACT,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C;EACF,CAAC,CAAC;EAEF,MAAMU,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAIX,SAAS,KAAKW,KAAK,EAAE;MACvBR,gBAAgB,CAACD,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5D,CAAC,MAAM;MACLD,YAAY,CAACU,KAAK,CAAC;MACnBR,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMS,cAAc,GAAGA,CAACvB,QAAQ,EAAEC,QAAQ,KAAK;IAC7C,IAAID,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEwB,MAAM,EAAE,cAAc;QAAEC,KAAK,EAAE;MAA0B,CAAC;IACrE,CAAC,MAAM,IAAIzB,QAAQ,IAAIC,QAAQ,EAAE;MAC/B,OAAO;QAAEuB,MAAM,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAgC,CAAC;IACxE,CAAC,MAAM;MACL,OAAO;QAAED,MAAM,EAAE,UAAU;QAAEC,KAAK,EAAE;MAA8B,CAAC;IACrE;EACF,CAAC;EAED,oBACEjC,OAAA;IAAKkC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BnC,OAAA;MAAOkC,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBACpDnC,OAAA;QAAOkC,SAAS,EAAC,YAAY;QAAAC,QAAA,eAC3BnC,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,KAAK,CAAE;YAAAM,QAAA,EAClC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,MAAM,CAAE;YAAAM,QAAA,EACnC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,UAAU,CAAE;YAAAM,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,OAAO,CAAE;YAAAM,QAAA,EACpC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,UAAU,CAAE;YAAAM,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YAAIkC,SAAS,EAAC,gFAAgF;YAAAC,QAAA,EAAC;UAE/F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YACEkC,SAAS,EAAC,iHAAiH;YAC3HE,OAAO,EAAEA,CAAA,KAAMP,UAAU,CAAC,UAAU,CAAE;YAAAM,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxC,OAAA;YAAIkC,SAAS,EAAC,gFAAgF;YAAAC,QAAA,EAAC;UAE/F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACRxC,OAAA;QAAOkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EACjDrB,OAAO,gBACNd,OAAA;UAAAmC,QAAA,eACEnC,OAAA;YAAIyC,OAAO,EAAC,GAAG;YAACP,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eAChDnC,OAAA;cAAKkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CnC,OAAA,CAACF,MAAM;gBAACoC,SAAS,EAAC;cAAyC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DxC,OAAA;gBAAMkC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACHf,UAAU,CAACiB,MAAM,KAAK,CAAC,gBACzB1C,OAAA;UAAAmC,QAAA,eACEnC,OAAA;YAAIyC,OAAO,EAAC,GAAG;YAACP,SAAS,EAAC,wBAAwB;YAAAC,QAAA,eAChDnC,OAAA;cAAKkC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GAELf,UAAU,CAACkB,GAAG,CAAEC,IAAI,IAAK;UACvB,MAAMC,WAAW,GAAGd,cAAc,CAACa,IAAI,CAACpC,QAAQ,EAAEoC,IAAI,CAACnC,QAAQ,CAAC;UAChE,oBACET,OAAA;YAA8BkC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC1DnC,OAAA;cAAIkC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1ES,IAAI,CAACzC;YAAG;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCnC,OAAA;gBAAKkC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eAChCnC,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAKkC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAES,IAAI,CAACxC;kBAAI;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACnEI,IAAI,CAACpC,QAAQ,IAAIoC,IAAI,CAACnC,QAAQ,iBAC7BT,OAAA;oBAAKkC,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDnC,OAAA,CAACH,aAAa;sBAACqC,SAAS,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE5C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DS,IAAI,CAACvC;YAAQ;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,GAAC,GAC/D,EAACS,IAAI,CAACtC,KAAK,CAACwC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DS,IAAI,CAACpC;YAAQ;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCnC,OAAA;gBAAMkC,SAAS,EAAE,4DAA4DW,WAAW,CAACZ,KAAK,EAAG;gBAAAE,QAAA,EAC9FU,WAAW,CAACb;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DS,IAAI,CAAClC;YAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLxC,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAC/DnC,OAAA;gBAAKkC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BnC,OAAA;kBACEoC,OAAO,EAAEA,CAAA,KAAMrB,MAAM,IAAIA,MAAM,CAAC6B,IAAI,CAAE;kBACtCV,SAAS,EAAC,mCAAmC;kBAC7Ca,KAAK,EAAC,cAAc;kBAAAZ,QAAA,eAEpBnC,OAAA,CAACN,IAAI;oBAACwC,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACTxC,OAAA;kBACEoC,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,IAAIA,QAAQ,CAAC4B,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC1C,EAAE,CAAE;kBACzDgC,SAAS,EAAC,iCAAiC;kBAC3Ca,KAAK,EAAC,gBAAgB;kBAAAZ,QAAA,eAEtBnC,OAAA,CAACL,MAAM;oBAACuC,SAAS,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnDII,IAAI,CAACI,GAAG,IAAIJ,IAAI,CAAC1C,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoD1B,CAAC;QAET,CAAC;MACA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEPf,UAAU,CAACiB,MAAM,KAAK,CAAC,iBACtB1C,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnC,OAAA;QAAGkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAyC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtB,EAAA,CAxKIN,cAAc;AAAAqC,EAAA,GAAdrC,cAAc;AA0KpB,eAAeA,cAAc;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}