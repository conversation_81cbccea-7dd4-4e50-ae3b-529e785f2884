const mongoose = require('mongoose');

const aisleSchema = new mongoose.Schema({
  aisle_id: {
    type: Number,
    required: true,
    unique: true
  },
  aisle: {
    type: String,
    required: true,
    trim: true
  }
}, {
  timestamps: true,
  collection: 'instacart_aisles'
});

// Index for better query performance
aisleSchema.index({ aisle_id: 1 });
aisleSchema.index({ aisle: 1 });

module.exports = mongoose.model('InstacartAisle', aisleSchema);
