{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Inventory\\\\AddInventoryModal.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { X, Loader } from 'lucide-react';\nimport { productsAPI, suppliersAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AddInventoryModal = ({\n  isOpen,\n  onClose,\n  product = null,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [formData, setFormData] = useState({\n    sku: '',\n    name: '',\n    category: '',\n    price: '',\n    cost: '',\n    quantity: '',\n    minStock: '',\n    maxStock: '',\n    supplier: '',\n    description: '',\n    location: {\n      warehouse: '',\n      aisle: '',\n      shelf: '',\n      bin: ''\n    },\n    reorderPoint: '',\n    reorderQuantity: '',\n    tags: ''\n  });\n  const categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys', 'Footwear'];\n  useEffect(() => {\n    if (isOpen) {\n      fetchSuppliers();\n      if (product) {\n        var _product$supplier, _product$location, _product$location2, _product$location3, _product$location4, _product$tags;\n        // Edit mode - populate form with existing product data\n        setFormData({\n          sku: product.sku || '',\n          name: product.name || '',\n          category: product.category || '',\n          price: product.price || '',\n          cost: product.cost || '',\n          quantity: product.quantity || '',\n          minStock: product.minStock || '',\n          maxStock: product.maxStock || '',\n          supplier: ((_product$supplier = product.supplier) === null || _product$supplier === void 0 ? void 0 : _product$supplier._id) || product.supplier || '',\n          description: product.description || '',\n          location: {\n            warehouse: ((_product$location = product.location) === null || _product$location === void 0 ? void 0 : _product$location.warehouse) || '',\n            aisle: ((_product$location2 = product.location) === null || _product$location2 === void 0 ? void 0 : _product$location2.aisle) || '',\n            shelf: ((_product$location3 = product.location) === null || _product$location3 === void 0 ? void 0 : _product$location3.shelf) || '',\n            bin: ((_product$location4 = product.location) === null || _product$location4 === void 0 ? void 0 : _product$location4.bin) || ''\n          },\n          reorderPoint: product.reorderPoint || '',\n          reorderQuantity: product.reorderQuantity || '',\n          tags: ((_product$tags = product.tags) === null || _product$tags === void 0 ? void 0 : _product$tags.join(', ')) || ''\n        });\n      } else {\n        // Add mode - reset form\n        setFormData({\n          sku: '',\n          name: '',\n          category: '',\n          price: '',\n          cost: '',\n          quantity: '',\n          minStock: '',\n          maxStock: '',\n          supplier: '',\n          description: '',\n          location: {\n            warehouse: '',\n            aisle: '',\n            shelf: '',\n            bin: ''\n          },\n          reorderPoint: '',\n          reorderQuantity: '',\n          tags: ''\n        });\n      }\n    }\n  }, [isOpen, product]);\n  const fetchSuppliers = async () => {\n    try {\n      const response = await suppliersAPI.getAll({\n        status: 'active'\n      });\n      setSuppliers(response.data.suppliers || []);\n    } catch (error) {\n      console.error('Failed to fetch suppliers:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name.startsWith('location.')) {\n      const locationField = name.split('.')[1];\n      setFormData(prev => ({\n        ...prev,\n        location: {\n          ...prev.location,\n          [locationField]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        cost: parseFloat(formData.cost),\n        quantity: parseInt(formData.quantity),\n        minStock: parseInt(formData.minStock),\n        maxStock: formData.maxStock ? parseInt(formData.maxStock) : undefined,\n        reorderPoint: formData.reorderPoint ? parseInt(formData.reorderPoint) : undefined,\n        reorderQuantity: formData.reorderQuantity ? parseInt(formData.reorderQuantity) : undefined,\n        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []\n      };\n\n      // Remove empty location fields\n      if (!submitData.location.warehouse && !submitData.location.aisle && !submitData.location.shelf && !submitData.location.bin) {\n        delete submitData.location;\n      }\n      let response;\n      if (product) {\n        // Update existing product\n        response = await productsAPI.update(product._id, submitData);\n        toast.success('Product updated successfully');\n      } else {\n        // Create new product\n        response = await productsAPI.create(submitData);\n        toast.success('Product added successfully');\n      }\n      if (onSuccess) {\n        onSuccess(response.data);\n      }\n      onClose();\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      console.error('Error saving product:', error);\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.errors) {\n        const errorMessages = error.response.data.errors.map(err => err.msg).join(', ');\n        toast.error(errorMessages);\n      } else if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.error) {\n        toast.error(error.response.data.error);\n      } else {\n        toast.error('Failed to save product');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 overflow-y-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n        onClick: onClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900\",\n              children: product ? 'Edit Product' : 'Add New Product'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onClose,\n              className: \"text-gray-400 hover:text-gray-600\",\n              disabled: loading,\n              children: /*#__PURE__*/_jsxDEV(X, {\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-3\",\n                children: \"Basic Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"SKU *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"sku\",\n                    value: formData.sku,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Category *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"category\",\n                    value: formData.category,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Category\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 23\n                    }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: category,\n                      children: category\n                    }, category, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Supplier *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"supplier\",\n                    value: formData.supplier,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Supplier\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 23\n                    }, this), suppliers.map(supplier => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: supplier._id,\n                      children: supplier.name\n                    }, supplier._id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Product Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"name\",\n                value: formData.name,\n                onChange: handleChange,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                required: true,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"description\",\n                value: formData.description,\n                onChange: handleChange,\n                rows: 2,\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-3\",\n                children: \"Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Price ($) *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    step: \"0.01\",\n                    name: \"price\",\n                    value: formData.price,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Cost ($) *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    step: \"0.01\",\n                    name: \"cost\",\n                    value: formData.cost,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-3\",\n                children: \"Inventory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Current Quantity *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"quantity\",\n                    value: formData.quantity,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Min Stock Level *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"minStock\",\n                    value: formData.minStock,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    required: true,\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Max Stock Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"maxStock\",\n                    value: formData.maxStock,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Reorder Point\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"reorderPoint\",\n                    value: formData.reorderPoint,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Reorder Quantity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"number\",\n                    name: \"reorderQuantity\",\n                    value: formData.reorderQuantity,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Tags (comma separated)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"tags\",\n                    value: formData.tags,\n                    onChange: handleChange,\n                    placeholder: \"e.g. electronics, mobile, smartphone\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-gray-900 mb-3\",\n                children: \"Storage Location\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Warehouse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"location.warehouse\",\n                    value: formData.location.warehouse,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Aisle\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"location.aisle\",\n                    value: formData.location.aisle,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Shelf\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"location.shelf\",\n                    value: formData.location.shelf,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Bin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"location.bin\",\n                    value: formData.location.bin,\n                    onChange: handleChange,\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n                    disabled: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end space-x-3 pt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: onClose,\n                className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\",\n                children: \"Add Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(AddInventoryModal, \"uiRB6dTzwbg7b70g5VC2zEA5y/c=\");\n_c = AddInventoryModal;\nexport default AddInventoryModal;\nvar _c;\n$RefreshReg$(_c, \"AddInventoryModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "X", "Loader", "productsAPI", "suppliersAPI", "toast", "jsxDEV", "_jsxDEV", "AddInventoryModal", "isOpen", "onClose", "product", "onSuccess", "_s", "loading", "setLoading", "suppliers", "setSuppliers", "formData", "setFormData", "sku", "name", "category", "price", "cost", "quantity", "minStock", "maxStock", "supplier", "description", "location", "warehouse", "aisle", "shelf", "bin", "reorderPoint", "reorderQuantity", "tags", "categories", "fetchSuppliers", "_product$supplier", "_product$location", "_product$location2", "_product$location3", "_product$location4", "_product$tags", "_id", "join", "response", "getAll", "status", "data", "error", "console", "handleChange", "e", "value", "target", "startsWith", "locationField", "split", "prev", "handleSubmit", "preventDefault", "submitData", "parseFloat", "parseInt", "undefined", "map", "tag", "trim", "filter", "update", "success", "create", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errors", "errorMessages", "err", "msg", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "onSubmit", "type", "onChange", "required", "rows", "step", "placeholder", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Inventory/AddInventoryModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { X, Loader } from 'lucide-react';\nimport { productsAPI, suppliersAPI } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst AddInventoryModal = ({ isOpen, onClose, product = null, onSuccess }) => {\n  const [loading, setLoading] = useState(false);\n  const [suppliers, setSuppliers] = useState([]);\n  const [formData, setFormData] = useState({\n    sku: '',\n    name: '',\n    category: '',\n    price: '',\n    cost: '',\n    quantity: '',\n    minStock: '',\n    maxStock: '',\n    supplier: '',\n    description: '',\n    location: {\n      warehouse: '',\n      aisle: '',\n      shelf: '',\n      bin: ''\n    },\n    reorderPoint: '',\n    reorderQuantity: '',\n    tags: ''\n  });\n\n  const categories = [\n    'Electronics',\n    'Clothing',\n    'Books',\n    'Home & Garden',\n    'Sports',\n    'Toys',\n    'Footwear'\n  ];\n\n  useEffect(() => {\n    if (isOpen) {\n      fetchSuppliers();\n      if (product) {\n        // Edit mode - populate form with existing product data\n        setFormData({\n          sku: product.sku || '',\n          name: product.name || '',\n          category: product.category || '',\n          price: product.price || '',\n          cost: product.cost || '',\n          quantity: product.quantity || '',\n          minStock: product.minStock || '',\n          maxStock: product.maxStock || '',\n          supplier: product.supplier?._id || product.supplier || '',\n          description: product.description || '',\n          location: {\n            warehouse: product.location?.warehouse || '',\n            aisle: product.location?.aisle || '',\n            shelf: product.location?.shelf || '',\n            bin: product.location?.bin || ''\n          },\n          reorderPoint: product.reorderPoint || '',\n          reorderQuantity: product.reorderQuantity || '',\n          tags: product.tags?.join(', ') || ''\n        });\n      } else {\n        // Add mode - reset form\n        setFormData({\n          sku: '',\n          name: '',\n          category: '',\n          price: '',\n          cost: '',\n          quantity: '',\n          minStock: '',\n          maxStock: '',\n          supplier: '',\n          description: '',\n          location: {\n            warehouse: '',\n            aisle: '',\n            shelf: '',\n            bin: ''\n          },\n          reorderPoint: '',\n          reorderQuantity: '',\n          tags: ''\n        });\n      }\n    }\n  }, [isOpen, product]);\n\n  const fetchSuppliers = async () => {\n    try {\n      const response = await suppliersAPI.getAll({ status: 'active' });\n      setSuppliers(response.data.suppliers || []);\n    } catch (error) {\n      console.error('Failed to fetch suppliers:', error);\n    }\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    if (name.startsWith('location.')) {\n      const locationField = name.split('.')[1];\n      setFormData(prev => ({\n        ...prev,\n        location: {\n          ...prev.location,\n          [locationField]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [name]: value\n      }));\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Prepare data for API\n      const submitData = {\n        ...formData,\n        price: parseFloat(formData.price),\n        cost: parseFloat(formData.cost),\n        quantity: parseInt(formData.quantity),\n        minStock: parseInt(formData.minStock),\n        maxStock: formData.maxStock ? parseInt(formData.maxStock) : undefined,\n        reorderPoint: formData.reorderPoint ? parseInt(formData.reorderPoint) : undefined,\n        reorderQuantity: formData.reorderQuantity ? parseInt(formData.reorderQuantity) : undefined,\n        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []\n      };\n\n      // Remove empty location fields\n      if (!submitData.location.warehouse && !submitData.location.aisle &&\n          !submitData.location.shelf && !submitData.location.bin) {\n        delete submitData.location;\n      }\n\n      let response;\n      if (product) {\n        // Update existing product\n        response = await productsAPI.update(product._id, submitData);\n        toast.success('Product updated successfully');\n      } else {\n        // Create new product\n        response = await productsAPI.create(submitData);\n        toast.success('Product added successfully');\n      }\n\n      if (onSuccess) {\n        onSuccess(response.data);\n      }\n      onClose();\n    } catch (error) {\n      console.error('Error saving product:', error);\n      if (error.response?.data?.errors) {\n        const errorMessages = error.response.data.errors.map(err => err.msg).join(', ');\n        toast.error(errorMessages);\n      } else if (error.response?.data?.error) {\n        toast.error(error.response.data.error);\n      } else {\n        toast.error('Failed to save product');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n        <div className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" onClick={onClose}></div>\n\n        <div className=\"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full\">\n          <div className=\"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">\n                {product ? 'Edit Product' : 'Add New Product'}\n              </h3>\n              <button\n                onClick={onClose}\n                className=\"text-gray-400 hover:text-gray-600\"\n                disabled={loading}\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Basic Information */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Basic Information</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      SKU *\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"sku\"\n                      value={formData.sku}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Category *\n                    </label>\n                    <select\n                      name=\"category\"\n                      value={formData.category}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    >\n                      <option value=\"\">Select Category</option>\n                      {categories.map(category => (\n                        <option key={category} value={category}>{category}</option>\n                      ))}\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Supplier *\n                    </label>\n                    <select\n                      name=\"supplier\"\n                      value={formData.supplier}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    >\n                      <option value=\"\">Select Supplier</option>\n                      {suppliers.map(supplier => (\n                        <option key={supplier._id} value={supplier._id}>{supplier.name}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Product Name *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                  required\n                  disabled={loading}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  name=\"description\"\n                  value={formData.description}\n                  onChange={handleChange}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Pricing */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Pricing</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Price ($) *\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Cost ($) *\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"cost\"\n                      value={formData.cost}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Inventory */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Inventory</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Current Quantity *\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"quantity\"\n                      value={formData.quantity}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Min Stock Level *\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"minStock\"\n                      value={formData.minStock}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      required\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Max Stock Level\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"maxStock\"\n                      value={formData.maxStock}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Reorder Point\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"reorderPoint\"\n                      value={formData.reorderPoint}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Reorder Quantity\n                    </label>\n                    <input\n                      type=\"number\"\n                      name=\"reorderQuantity\"\n                      value={formData.reorderQuantity}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tags (comma separated)\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"tags\"\n                      value={formData.tags}\n                      onChange={handleChange}\n                      placeholder=\"e.g. electronics, mobile, smartphone\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Location */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Storage Location</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Warehouse\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"location.warehouse\"\n                      value={formData.location.warehouse}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Aisle\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"location.aisle\"\n                      value={formData.location.aisle}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Shelf\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"location.shelf\"\n                      value={formData.location.shelf}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Bin\n                    </label>\n                    <input\n                      type=\"text\"\n                      name=\"location.bin\"\n                      value={formData.location.bin}\n                      onChange={handleChange}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n                      disabled={loading}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 pt-4\">\n                <button\n                  type=\"button\"\n                  onClick={onClose}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  className=\"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\"\n                >\n                  Add Product\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AddInventoryModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,CAAC,EAAEC,MAAM,QAAQ,cAAc;AACxC,SAASC,WAAW,EAAEC,YAAY,QAAQ,oBAAoB;AAC9D,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO,GAAG,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC;IACDC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACjB,aAAa,EACb,UAAU,EACV,OAAO,EACP,eAAe,EACf,QAAQ,EACR,MAAM,EACN,UAAU,CACX;EAEDtC,SAAS,CAAC,MAAM;IACd,IAAIS,MAAM,EAAE;MACV8B,cAAc,CAAC,CAAC;MAChB,IAAI5B,OAAO,EAAE;QAAA,IAAA6B,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,aAAA;QACX;QACA1B,WAAW,CAAC;UACVC,GAAG,EAAET,OAAO,CAACS,GAAG,IAAI,EAAE;UACtBC,IAAI,EAAEV,OAAO,CAACU,IAAI,IAAI,EAAE;UACxBC,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,EAAE;UAChCC,KAAK,EAAEZ,OAAO,CAACY,KAAK,IAAI,EAAE;UAC1BC,IAAI,EAAEb,OAAO,CAACa,IAAI,IAAI,EAAE;UACxBC,QAAQ,EAAEd,OAAO,CAACc,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAEf,OAAO,CAACe,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAEhB,OAAO,CAACgB,QAAQ,IAAI,EAAE;UAChCC,QAAQ,EAAE,EAAAY,iBAAA,GAAA7B,OAAO,CAACiB,QAAQ,cAAAY,iBAAA,uBAAhBA,iBAAA,CAAkBM,GAAG,KAAInC,OAAO,CAACiB,QAAQ,IAAI,EAAE;UACzDC,WAAW,EAAElB,OAAO,CAACkB,WAAW,IAAI,EAAE;UACtCC,QAAQ,EAAE;YACRC,SAAS,EAAE,EAAAU,iBAAA,GAAA9B,OAAO,CAACmB,QAAQ,cAAAW,iBAAA,uBAAhBA,iBAAA,CAAkBV,SAAS,KAAI,EAAE;YAC5CC,KAAK,EAAE,EAAAU,kBAAA,GAAA/B,OAAO,CAACmB,QAAQ,cAAAY,kBAAA,uBAAhBA,kBAAA,CAAkBV,KAAK,KAAI,EAAE;YACpCC,KAAK,EAAE,EAAAU,kBAAA,GAAAhC,OAAO,CAACmB,QAAQ,cAAAa,kBAAA,uBAAhBA,kBAAA,CAAkBV,KAAK,KAAI,EAAE;YACpCC,GAAG,EAAE,EAAAU,kBAAA,GAAAjC,OAAO,CAACmB,QAAQ,cAAAc,kBAAA,uBAAhBA,kBAAA,CAAkBV,GAAG,KAAI;UAChC,CAAC;UACDC,YAAY,EAAExB,OAAO,CAACwB,YAAY,IAAI,EAAE;UACxCC,eAAe,EAAEzB,OAAO,CAACyB,eAAe,IAAI,EAAE;UAC9CC,IAAI,EAAE,EAAAQ,aAAA,GAAAlC,OAAO,CAAC0B,IAAI,cAAAQ,aAAA,uBAAZA,aAAA,CAAcE,IAAI,CAAC,IAAI,CAAC,KAAI;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA5B,WAAW,CAAC;UACVC,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,WAAW,EAAE,EAAE;UACfC,QAAQ,EAAE;YACRC,SAAS,EAAE,EAAE;YACbC,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE,EAAE;YACTC,GAAG,EAAE;UACP,CAAC;UACDC,YAAY,EAAE,EAAE;UAChBC,eAAe,EAAE,EAAE;UACnBC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAAC5B,MAAM,EAAEE,OAAO,CAAC,CAAC;EAErB,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAM5C,YAAY,CAAC6C,MAAM,CAAC;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;MAChEjC,YAAY,CAAC+B,QAAQ,CAACG,IAAI,CAACnC,SAAS,IAAI,EAAE,CAAC;IAC7C,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,MAAME,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElC,IAAI;MAAEmC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIpC,IAAI,CAACqC,UAAU,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMC,aAAa,GAAGtC,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxCzC,WAAW,CAAC0C,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP/B,QAAQ,EAAE;UACR,GAAG+B,IAAI,CAAC/B,QAAQ;UAChB,CAAC6B,aAAa,GAAGH;QACnB;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLrC,WAAW,CAAC0C,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACxC,IAAI,GAAGmC;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOP,CAAC,IAAK;IAChCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClBhD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAMiD,UAAU,GAAG;QACjB,GAAG9C,QAAQ;QACXK,KAAK,EAAE0C,UAAU,CAAC/C,QAAQ,CAACK,KAAK,CAAC;QACjCC,IAAI,EAAEyC,UAAU,CAAC/C,QAAQ,CAACM,IAAI,CAAC;QAC/BC,QAAQ,EAAEyC,QAAQ,CAAChD,QAAQ,CAACO,QAAQ,CAAC;QACrCC,QAAQ,EAAEwC,QAAQ,CAAChD,QAAQ,CAACQ,QAAQ,CAAC;QACrCC,QAAQ,EAAET,QAAQ,CAACS,QAAQ,GAAGuC,QAAQ,CAAChD,QAAQ,CAACS,QAAQ,CAAC,GAAGwC,SAAS;QACrEhC,YAAY,EAAEjB,QAAQ,CAACiB,YAAY,GAAG+B,QAAQ,CAAChD,QAAQ,CAACiB,YAAY,CAAC,GAAGgC,SAAS;QACjF/B,eAAe,EAAElB,QAAQ,CAACkB,eAAe,GAAG8B,QAAQ,CAAChD,QAAQ,CAACkB,eAAe,CAAC,GAAG+B,SAAS;QAC1F9B,IAAI,EAAEnB,QAAQ,CAACmB,IAAI,GAAGnB,QAAQ,CAACmB,IAAI,CAACuB,KAAK,CAAC,GAAG,CAAC,CAACQ,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACF,GAAG,IAAIA,GAAG,CAAC,GAAG;MAC7F,CAAC;;MAED;MACA,IAAI,CAACL,UAAU,CAAClC,QAAQ,CAACC,SAAS,IAAI,CAACiC,UAAU,CAAClC,QAAQ,CAACE,KAAK,IAC5D,CAACgC,UAAU,CAAClC,QAAQ,CAACG,KAAK,IAAI,CAAC+B,UAAU,CAAClC,QAAQ,CAACI,GAAG,EAAE;QAC1D,OAAO8B,UAAU,CAAClC,QAAQ;MAC5B;MAEA,IAAIkB,QAAQ;MACZ,IAAIrC,OAAO,EAAE;QACX;QACAqC,QAAQ,GAAG,MAAM7C,WAAW,CAACqE,MAAM,CAAC7D,OAAO,CAACmC,GAAG,EAAEkB,UAAU,CAAC;QAC5D3D,KAAK,CAACoE,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACL;QACAzB,QAAQ,GAAG,MAAM7C,WAAW,CAACuE,MAAM,CAACV,UAAU,CAAC;QAC/C3D,KAAK,CAACoE,OAAO,CAAC,4BAA4B,CAAC;MAC7C;MAEA,IAAI7D,SAAS,EAAE;QACbA,SAAS,CAACoC,QAAQ,CAACG,IAAI,CAAC;MAC1B;MACAzC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO0C,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdzB,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,KAAAuB,eAAA,GAAIvB,KAAK,CAACJ,QAAQ,cAAA2B,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,eAApBA,oBAAA,CAAsBG,MAAM,EAAE;QAChC,MAAMC,aAAa,GAAG5B,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAAC4B,MAAM,CAACX,GAAG,CAACa,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACnC,IAAI,CAAC,IAAI,CAAC;QAC/E1C,KAAK,CAAC+C,KAAK,CAAC4B,aAAa,CAAC;MAC5B,CAAC,MAAM,KAAAH,gBAAA,GAAIzB,KAAK,CAACJ,QAAQ,cAAA6B,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,eAApBA,qBAAA,CAAsB1B,KAAK,EAAE;QACtC/C,KAAK,CAAC+C,KAAK,CAACA,KAAK,CAACJ,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;MACxC,CAAC,MAAM;QACL/C,KAAK,CAAC+C,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACF,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACN,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAK4E,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eACjD7E,OAAA;MAAK4E,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBACxG7E,OAAA;QAAK4E,SAAS,EAAC,4DAA4D;QAACE,OAAO,EAAE3E;MAAQ;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEpGlF,OAAA;QAAK4E,SAAS,EAAC,2JAA2J;QAAAC,QAAA,eACxK7E,OAAA;UAAK4E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD7E,OAAA;YAAK4E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD7E,OAAA;cAAI4E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC9CzE,OAAO,GAAG,cAAc,GAAG;YAAiB;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACLlF,OAAA;cACE8E,OAAO,EAAE3E,OAAQ;cACjByE,SAAS,EAAC,mCAAmC;cAC7CO,QAAQ,EAAE5E,OAAQ;cAAAsE,QAAA,eAElB7E,OAAA,CAACN,CAAC;gBAACkF,SAAS,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlF,OAAA;YAAMoF,QAAQ,EAAE7B,YAAa;YAACqB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAEjD7E,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAI4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7ElF,OAAA;gBAAK4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,KAAK;oBACVmC,KAAK,EAAEtC,QAAQ,CAACE,GAAI;oBACpByE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEc,IAAI,EAAC,UAAU;oBACfmC,KAAK,EAAEtC,QAAQ,CAACI,QAAS;oBACzBuE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E,OAAQ;oBAAAsE,QAAA,gBAElB7E,OAAA;sBAAQiD,KAAK,EAAC,EAAE;sBAAA4B,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACxCnD,UAAU,CAAC8B,GAAG,CAAC9C,QAAQ,iBACtBf,OAAA;sBAAuBiD,KAAK,EAAElC,QAAS;sBAAA8D,QAAA,EAAE9D;oBAAQ,GAApCA,QAAQ;sBAAAgE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAqC,CAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEc,IAAI,EAAC,UAAU;oBACfmC,KAAK,EAAEtC,QAAQ,CAACU,QAAS;oBACzBiE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E,OAAQ;oBAAAsE,QAAA,gBAElB7E,OAAA;sBAAQiD,KAAK,EAAC,EAAE;sBAAA4B,QAAA,EAAC;oBAAe;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACxCzE,SAAS,CAACoD,GAAG,CAACxC,QAAQ,iBACrBrB,OAAA;sBAA2BiD,KAAK,EAAE5B,QAAQ,CAACkB,GAAI;sBAAAsC,QAAA,EAAExD,QAAQ,CAACP;oBAAI,GAAjDO,QAAQ,CAACkB,GAAG;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA8C,CACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlF,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAO4E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACEqF,IAAI,EAAC,MAAM;gBACXvE,IAAI,EAAC,MAAM;gBACXmC,KAAK,EAAEtC,QAAQ,CAACG,IAAK;gBACrBwE,QAAQ,EAAEvC,YAAa;gBACvB6B,SAAS,EAAC,oIAAoI;gBAC9IW,QAAQ;gBACRJ,QAAQ,EAAE5E;cAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlF,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAO4E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlF,OAAA;gBACEc,IAAI,EAAC,aAAa;gBAClBmC,KAAK,EAAEtC,QAAQ,CAACW,WAAY;gBAC5BgE,QAAQ,EAAEvC,YAAa;gBACvByC,IAAI,EAAE,CAAE;gBACRZ,SAAS,EAAC,oIAAoI;gBAC9IO,QAAQ,EAAE5E;cAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlF,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAI4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElF,OAAA;gBAAK4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbI,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,OAAO;oBACZmC,KAAK,EAAEtC,QAAQ,CAACK,KAAM;oBACtBsE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbI,IAAI,EAAC,MAAM;oBACX3E,IAAI,EAAC,MAAM;oBACXmC,KAAK,EAAEtC,QAAQ,CAACM,IAAK;oBACrBqE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAI4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrElF,OAAA;gBAAK4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbvE,IAAI,EAAC,UAAU;oBACfmC,KAAK,EAAEtC,QAAQ,CAACO,QAAS;oBACzBoE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbvE,IAAI,EAAC,UAAU;oBACfmC,KAAK,EAAEtC,QAAQ,CAACQ,QAAS;oBACzBmE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IW,QAAQ;oBACRJ,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbvE,IAAI,EAAC,UAAU;oBACfmC,KAAK,EAAEtC,QAAQ,CAACS,QAAS;oBACzBkE,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbvE,IAAI,EAAC,cAAc;oBACnBmC,KAAK,EAAEtC,QAAQ,CAACiB,YAAa;oBAC7B0D,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlF,OAAA;gBAAK4E,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBACzD7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,QAAQ;oBACbvE,IAAI,EAAC,iBAAiB;oBACtBmC,KAAK,EAAEtC,QAAQ,CAACkB,eAAgB;oBAChCyD,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,MAAM;oBACXmC,KAAK,EAAEtC,QAAQ,CAACmB,IAAK;oBACrBwD,QAAQ,EAAEvC,YAAa;oBACvB2C,WAAW,EAAC,sCAAsC;oBAClDd,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlF,OAAA;cAAA6E,QAAA,gBACE7E,OAAA;gBAAI4E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ElF,OAAA;gBAAK4E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD7E,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,oBAAoB;oBACzBmC,KAAK,EAAEtC,QAAQ,CAACY,QAAQ,CAACC,SAAU;oBACnC8D,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,gBAAgB;oBACrBmC,KAAK,EAAEtC,QAAQ,CAACY,QAAQ,CAACE,KAAM;oBAC/B6D,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,gBAAgB;oBACrBmC,KAAK,EAAEtC,QAAQ,CAACY,QAAQ,CAACG,KAAM;oBAC/B4D,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlF,OAAA;kBAAA6E,QAAA,gBACE7E,OAAA;oBAAO4E,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlF,OAAA;oBACEqF,IAAI,EAAC,MAAM;oBACXvE,IAAI,EAAC,cAAc;oBACnBmC,KAAK,EAAEtC,QAAQ,CAACY,QAAQ,CAACI,GAAI;oBAC7B2D,QAAQ,EAAEvC,YAAa;oBACvB6B,SAAS,EAAC,oIAAoI;oBAC9IO,QAAQ,EAAE5E;kBAAQ;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlF,OAAA;cAAK4E,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9C7E,OAAA;gBACEqF,IAAI,EAAC,QAAQ;gBACbP,OAAO,EAAE3E,OAAQ;gBACjByE,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,EACvF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlF,OAAA;gBACEqF,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAChF;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAteIL,iBAAiB;AAAA0F,EAAA,GAAjB1F,iBAAiB;AAwevB,eAAeA,iBAAiB;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}