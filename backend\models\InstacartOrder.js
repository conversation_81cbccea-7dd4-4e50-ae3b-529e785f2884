const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  order_id: {
    type: Number,
    required: true,
    unique: true
  },
  user_id: {
    type: Number,
    required: true
  },
  eval_set: {
    type: String,
    required: true,
    enum: ['prior', 'train', 'test']
  },
  order_number: {
    type: Number,
    required: true
  },
  order_dow: {
    type: Number,
    required: true,
    min: 0,
    max: 6 // Day of week (0-6)
  },
  order_hour_of_day: {
    type: Number,
    required: true,
    min: 0,
    max: 23
  },
  days_since_prior_order: {
    type: Number,
    default: null
  }
}, {
  timestamps: true,
  collection: 'instacart_orders'
});

// Indexes for better query performance
orderSchema.index({ order_id: 1 });
orderSchema.index({ user_id: 1 });
orderSchema.index({ eval_set: 1 });
orderSchema.index({ order_dow: 1 });
orderSchema.index({ order_hour_of_day: 1 });
orderSchema.index({ user_id: 1, order_number: 1 });

module.exports = mongoose.model('InstacartOrder', orderSchema);
