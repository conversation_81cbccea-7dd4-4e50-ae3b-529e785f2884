{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\InstacartAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';\nimport { ShoppingCart, Package, TrendingUp, Calendar, Users } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\nconst InstacartAnalysis = () => {\n  _s();\n  var _summary$totalProduct, _summary$totalOrders, _summary$totalOrderPr;\n  const [summary, setSummary] = useState({});\n  const [topProducts, setTopProducts] = useState([]);\n  const [ordersByHour, setOrdersByHour] = useState([]);\n  const [ordersByDow, setOrdersByDow] = useState([]);\n  const [departmentStats, setDepartmentStats] = useState([]);\n  const [aisleStats, setAisleStats] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, []);\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      const [summaryRes, topProductsRes, ordersByHourRes, ordersByDowRes, departmentStatsRes, aisleStatsRes] = await Promise.all([axios.get('/api/instacart/analytics/summary'), axios.get('/api/instacart/analytics/top-products?limit=10'), axios.get('/api/instacart/analytics/orders-by-hour'), axios.get('/api/instacart/analytics/orders-by-dow'), axios.get('/api/instacart/analytics/department-stats'), axios.get('/api/instacart/analytics/aisle-stats?limit=15')]);\n      setSummary(summaryRes.data);\n      setTopProducts(topProductsRes.data);\n      setOrdersByHour(ordersByHourRes.data);\n      setOrdersByDow(ordersByDowRes.data);\n      setDepartmentStats(departmentStatsRes.data);\n      setAisleStats(aisleStatsRes.data);\n    } catch (error) {\n      console.error('Error fetching analytics data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: \"Instacart Market Basket Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: fetchAnalyticsData,\n        className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\",\n        children: \"Refresh Data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"h-8 w-8 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_summary$totalProduct = summary.totalProducts) === null || _summary$totalProduct === void 0 ? void 0 : _summary$totalProduct.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n            className: \"h-8 w-8 text-green-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_summary$totalOrders = summary.totalOrders) === null || _summary$totalOrders === void 0 ? void 0 : _summary$totalOrders.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"h-8 w-8 text-purple-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Departments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: summary.totalDepartments\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Users, {\n            className: \"h-8 w-8 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Aisles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: summary.totalAisles\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            className: \"h-8 w-8 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Order Items\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: (_summary$totalOrderPr = summary.totalOrderProducts) === null || _summary$totalOrderPr === void 0 ? void 0 : _summary$totalOrderPr.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Top 10 Most Ordered Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: topProducts,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"product_name\",\n              angle: -45,\n              textAnchor: \"end\",\n              height: 100,\n              fontSize: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"order_count\",\n              fill: \"#8884d8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Orders by Hour of Day\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: ordersByHour,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"_id\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"count\",\n              stroke: \"#8884d8\",\n              strokeWidth: 2\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Orders by Day of Week\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(BarChart, {\n            data: ordersByDow,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"day_name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Bar, {\n              dataKey: \"count\",\n              fill: \"#82ca9d\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Department Popularity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: 300,\n          children: /*#__PURE__*/_jsxDEV(PieChart, {\n            children: [/*#__PURE__*/_jsxDEV(Pie, {\n              data: departmentStats.slice(0, 8),\n              cx: \"50%\",\n              cy: \"50%\",\n              labelLine: false,\n              label: ({\n                department_name,\n                percent\n              }) => `${department_name} ${(percent * 100).toFixed(0)}%`,\n              outerRadius: 80,\n              fill: \"#8884d8\",\n              dataKey: \"total_orders\",\n              children: departmentStats.slice(0, 8).map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                fill: COLORS[index % COLORS.length]\n              }, `cell-${index}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Top Performing Aisles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Aisle Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Unique Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Reorder Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: aisleStats.map((aisle, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: aisle.aisle_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: aisle.total_orders.toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: aisle.unique_products_count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: [(aisle.reorder_rate * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, aisle.aisle_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(InstacartAnalysis, \"rCCy+GOCWJ4b8EiJ1i+onP1vwlc=\");\n_c = InstacartAnalysis;\nexport default InstacartAnalysis;\nvar _c;\n$RefreshReg$(_c, \"InstacartAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "Line<PERSON>hart", "Line", "ShoppingCart", "Package", "TrendingUp", "Calendar", "Users", "axios", "jsxDEV", "_jsxDEV", "COLORS", "InstacartAnalysis", "_s", "_summary$totalProduct", "_summary$totalOrders", "_summary$totalOrderPr", "summary", "set<PERSON>ummary", "topProducts", "setTopProducts", "ordersByHour", "setOrdersByHour", "ordersByDow", "setOrdersByDow", "departmentStats", "setDepartmentStats", "aisleStats", "setAisleStats", "loading", "setLoading", "fetchAnalyticsData", "summaryRes", "topProductsRes", "ordersByHourRes", "ordersByDowRes", "departmentStatsRes", "aisleStatsRes", "Promise", "all", "get", "data", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "totalProducts", "toLocaleString", "totalOrders", "totalDepartments", "totalAisles", "totalOrderProducts", "width", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "fontSize", "fill", "type", "stroke", "strokeWidth", "slice", "cx", "cy", "labelLine", "label", "department_name", "percent", "toFixed", "outerRadius", "map", "entry", "index", "length", "aisle", "aisle_name", "total_orders", "unique_products_count", "reorder_rate", "aisle_id", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/InstacartAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,\n  PieChart, Pie, Cell, LineChart, Line\n} from 'recharts';\nimport { ShoppingCart, Package, TrendingUp, Calendar, Users } from 'lucide-react';\nimport axios from 'axios';\n\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];\n\nconst InstacartAnalysis = () => {\n  const [summary, setSummary] = useState({});\n  const [topProducts, setTopProducts] = useState([]);\n  const [ordersByHour, setOrdersByHour] = useState([]);\n  const [ordersByDow, setOrdersByDow] = useState([]);\n  const [departmentStats, setDepartmentStats] = useState([]);\n  const [aisleStats, setAisleStats] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAnalyticsData();\n  }, []);\n\n  const fetchAnalyticsData = async () => {\n    try {\n      setLoading(true);\n      const [\n        summaryRes,\n        topProductsRes,\n        ordersByHourRes,\n        ordersByDowRes,\n        departmentStatsRes,\n        aisleStatsRes\n      ] = await Promise.all([\n        axios.get('/api/instacart/analytics/summary'),\n        axios.get('/api/instacart/analytics/top-products?limit=10'),\n        axios.get('/api/instacart/analytics/orders-by-hour'),\n        axios.get('/api/instacart/analytics/orders-by-dow'),\n        axios.get('/api/instacart/analytics/department-stats'),\n        axios.get('/api/instacart/analytics/aisle-stats?limit=15')\n      ]);\n\n      setSummary(summaryRes.data);\n      setTopProducts(topProductsRes.data);\n      setOrdersByHour(ordersByHourRes.data);\n      setOrdersByDow(ordersByDowRes.data);\n      setDepartmentStats(departmentStatsRes.data);\n      setAisleStats(aisleStatsRes.data);\n    } catch (error) {\n      console.error('Error fetching analytics data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">Instacart Market Basket Analysis</h1>\n        <button\n          onClick={fetchAnalyticsData}\n          className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\"\n        >\n          Refresh Data\n        </button>\n      </div>\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <div className=\"flex items-center\">\n            <Package className=\"h-8 w-8 text-blue-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Total Products</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{summary.totalProducts?.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <div className=\"flex items-center\">\n            <ShoppingCart className=\"h-8 w-8 text-green-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{summary.totalOrders?.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <div className=\"flex items-center\">\n            <TrendingUp className=\"h-8 w-8 text-purple-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Departments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{summary.totalDepartments}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <div className=\"flex items-center\">\n            <Users className=\"h-8 w-8 text-orange-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Aisles</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{summary.totalAisles}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <div className=\"flex items-center\">\n            <Calendar className=\"h-8 w-8 text-red-500\" />\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Order Items</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{summary.totalOrderProducts?.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Top Products */}\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Top 10 Most Ordered Products</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={topProducts}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis \n                dataKey=\"product_name\" \n                angle={-45}\n                textAnchor=\"end\"\n                height={100}\n                fontSize={10}\n              />\n              <YAxis />\n              <Tooltip />\n              <Bar dataKey=\"order_count\" fill=\"#8884d8\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Orders by Hour */}\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Orders by Hour of Day</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <LineChart data={ordersByHour}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"_id\" />\n              <YAxis />\n              <Tooltip />\n              <Line type=\"monotone\" dataKey=\"count\" stroke=\"#8884d8\" strokeWidth={2} />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Orders by Day of Week */}\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Orders by Day of Week</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <BarChart data={ordersByDow}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"day_name\" />\n              <YAxis />\n              <Tooltip />\n              <Bar dataKey=\"count\" fill=\"#82ca9d\" />\n            </BarChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Department Distribution */}\n        <div className=\"bg-white p-6 rounded-lg shadow-md\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Department Popularity</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <PieChart>\n              <Pie\n                data={departmentStats.slice(0, 8)}\n                cx=\"50%\"\n                cy=\"50%\"\n                labelLine={false}\n                label={({ department_name, percent }) => `${department_name} ${(percent * 100).toFixed(0)}%`}\n                outerRadius={80}\n                fill=\"#8884d8\"\n                dataKey=\"total_orders\"\n              >\n                {departmentStats.slice(0, 8).map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                ))}\n              </Pie>\n              <Tooltip />\n            </PieChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Top Aisles Table */}\n      <div className=\"bg-white rounded-lg shadow-md\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Top Performing Aisles</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Aisle Name\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Total Orders\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Unique Products\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Reorder Rate\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {aisleStats.map((aisle, index) => (\n                <tr key={aisle.aisle_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    {aisle.aisle_name}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {aisle.total_orders.toLocaleString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {aisle.unique_products_count}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {(aisle.reorder_rate * 100).toFixed(1)}%\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InstacartAnalysis;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EACxEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,QAC/B,UAAU;AACjB,SAASC,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,cAAc;AACjF,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAEjF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;EAC9B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdyC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CACJE,UAAU,EACVC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,CACd,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpB/B,KAAK,CAACgC,GAAG,CAAC,kCAAkC,CAAC,EAC7ChC,KAAK,CAACgC,GAAG,CAAC,gDAAgD,CAAC,EAC3DhC,KAAK,CAACgC,GAAG,CAAC,yCAAyC,CAAC,EACpDhC,KAAK,CAACgC,GAAG,CAAC,wCAAwC,CAAC,EACnDhC,KAAK,CAACgC,GAAG,CAAC,2CAA2C,CAAC,EACtDhC,KAAK,CAACgC,GAAG,CAAC,+CAA+C,CAAC,CAC3D,CAAC;MAEFtB,UAAU,CAACc,UAAU,CAACS,IAAI,CAAC;MAC3BrB,cAAc,CAACa,cAAc,CAACQ,IAAI,CAAC;MACnCnB,eAAe,CAACY,eAAe,CAACO,IAAI,CAAC;MACrCjB,cAAc,CAACW,cAAc,CAACM,IAAI,CAAC;MACnCf,kBAAkB,CAACU,kBAAkB,CAACK,IAAI,CAAC;MAC3Cb,aAAa,CAACS,aAAa,CAACI,IAAI,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEnB,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDnC,OAAA;QAAKkC,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEvC,OAAA;IAAKkC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BnC,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDnC,OAAA;QAAIkC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtFvC,OAAA;QACEwC,OAAO,EAAEnB,kBAAmB;QAC5Ba,SAAS,EAAC,iFAAiF;QAAAC,QAAA,EAC5F;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEnC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA,CAACN,OAAO;YAACwC,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnEvC,OAAA;cAAGkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAA/B,qBAAA,GAAEG,OAAO,CAACkC,aAAa,cAAArC,qBAAA,uBAArBA,qBAAA,CAAuBsC,cAAc,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA,CAACP,YAAY;YAACyC,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEvC,OAAA;cAAGkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAA9B,oBAAA,GAAEE,OAAO,CAACoC,WAAW,cAAAtC,oBAAA,uBAAnBA,oBAAA,CAAqBqC,cAAc,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA,CAACL,UAAU;YAACuC,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEvC,OAAA;cAAGkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE5B,OAAO,CAACqC;YAAgB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA,CAACH,KAAK;YAACqC,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3DvC,OAAA;cAAGkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE5B,OAAO,CAACsC;YAAW;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDnC,OAAA;UAAKkC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCnC,OAAA,CAACJ,QAAQ;YAACsC,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CvC,OAAA;YAAKkC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBnC,OAAA;cAAGkC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEvC,OAAA;cAAGkC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAA7B,qBAAA,GAAEC,OAAO,CAACuC,kBAAkB,cAAAxC,qBAAA,uBAA1BA,qBAAA,CAA4BoC,cAAc,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDnC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1FvC,OAAA,CAACb,mBAAmB;UAAC4D,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAAb,QAAA,eAC5CnC,OAAA,CAACnB,QAAQ;YAACkD,IAAI,EAAEtB,WAAY;YAAA0B,QAAA,gBAC1BnC,OAAA,CAACf,aAAa;cAACgE,eAAe,EAAC;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCvC,OAAA,CAACjB,KAAK;cACJmE,OAAO,EAAC,cAAc;cACtBC,KAAK,EAAE,CAAC,EAAG;cACXC,UAAU,EAAC,KAAK;cAChBJ,MAAM,EAAE,GAAI;cACZK,QAAQ,EAAE;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACFvC,OAAA,CAAChB,KAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTvC,OAAA,CAACd,OAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXvC,OAAA,CAAClB,GAAG;cAACoE,OAAO,EAAC,aAAa;cAACI,IAAI,EAAC;YAAS;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFvC,OAAA,CAACb,mBAAmB;UAAC4D,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAAb,QAAA,eAC5CnC,OAAA,CAACT,SAAS;YAACwC,IAAI,EAAEpB,YAAa;YAAAwB,QAAA,gBAC5BnC,OAAA,CAACf,aAAa;cAACgE,eAAe,EAAC;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCvC,OAAA,CAACjB,KAAK;cAACmE,OAAO,EAAC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBvC,OAAA,CAAChB,KAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTvC,OAAA,CAACd,OAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXvC,OAAA,CAACR,IAAI;cAAC+D,IAAI,EAAC,UAAU;cAACL,OAAO,EAAC,OAAO;cAACM,MAAM,EAAC,SAAS;cAACC,WAAW,EAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFvC,OAAA,CAACb,mBAAmB;UAAC4D,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAAb,QAAA,eAC5CnC,OAAA,CAACnB,QAAQ;YAACkD,IAAI,EAAElB,WAAY;YAAAsB,QAAA,gBAC1BnC,OAAA,CAACf,aAAa;cAACgE,eAAe,EAAC;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCvC,OAAA,CAACjB,KAAK;cAACmE,OAAO,EAAC;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BvC,OAAA,CAAChB,KAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTvC,OAAA,CAACd,OAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXvC,OAAA,CAAClB,GAAG;cAACoE,OAAO,EAAC,OAAO;cAACI,IAAI,EAAC;YAAS;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAGNvC,OAAA;QAAKkC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDnC,OAAA;UAAIkC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFvC,OAAA,CAACb,mBAAmB;UAAC4D,KAAK,EAAC,MAAM;UAACC,MAAM,EAAE,GAAI;UAAAb,QAAA,eAC5CnC,OAAA,CAACZ,QAAQ;YAAA+C,QAAA,gBACPnC,OAAA,CAACX,GAAG;cACF0C,IAAI,EAAEhB,eAAe,CAAC2C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAE;cAClCC,EAAE,EAAC,KAAK;cACRC,EAAE,EAAC,KAAK;cACRC,SAAS,EAAE,KAAM;cACjBC,KAAK,EAAEA,CAAC;gBAAEC,eAAe;gBAAEC;cAAQ,CAAC,KAAK,GAAGD,eAAe,IAAI,CAACC,OAAO,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAI;cAC7FC,WAAW,EAAE,EAAG;cAChBZ,IAAI,EAAC,SAAS;cACdJ,OAAO,EAAC,cAAc;cAAAf,QAAA,EAErBpB,eAAe,CAAC2C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACS,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC5CrE,OAAA,CAACV,IAAI;gBAAuBgE,IAAI,EAAErD,MAAM,CAACoE,KAAK,GAAGpE,MAAM,CAACqE,MAAM;cAAE,GAArD,QAAQD,KAAK,EAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAwC,CACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvC,OAAA,CAACd,OAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CnC,OAAA;QAAKkC,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDnC,OAAA;UAAIkC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNvC,OAAA;QAAKkC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BnC,OAAA;UAAOkC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDnC,OAAA;YAAOkC,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BnC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAIkC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAE/F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvC,OAAA;YAAOkC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDlB,UAAU,CAACkD,GAAG,CAAC,CAACI,KAAK,EAAEF,KAAK,kBAC3BrE,OAAA;cAAyBkC,SAAS,EAAEmC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;cAAAlC,QAAA,gBAC9EnC,OAAA;gBAAIkC,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAC1EoC,KAAK,CAACC;cAAU;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DoC,KAAK,CAACE,YAAY,CAAC/B,cAAc,CAAC;cAAC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9DoC,KAAK,CAACG;cAAqB;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACLvC,OAAA;gBAAIkC,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAC9D,CAACoC,KAAK,CAACI,YAAY,GAAG,GAAG,EAAEV,OAAO,CAAC,CAAC,CAAC,EAAC,GACzC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAZEgC,KAAK,CAACK,QAAQ;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAanB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpC,EAAA,CA/OID,iBAAiB;AAAA2E,EAAA,GAAjB3E,iBAAiB;AAiPvB,eAAeA,iBAAiB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}