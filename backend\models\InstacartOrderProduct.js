const mongoose = require('mongoose');

const orderProductSchema = new mongoose.Schema({
  order_id: {
    type: Number,
    required: true,
    ref: 'InstacartOrder'
  },
  product_id: {
    type: Number,
    required: true,
    ref: 'InstacartProduct'
  },
  add_to_cart_order: {
    type: Number,
    required: true
  },
  reordered: {
    type: Number,
    required: true,
    min: 0,
    max: 1 // Binary: 0 or 1
  }
}, {
  timestamps: true,
  collection: 'instacart_order_products'
});

// Compound indexes for better query performance
orderProductSchema.index({ order_id: 1, product_id: 1 }, { unique: true });
orderProductSchema.index({ order_id: 1 });
orderProductSchema.index({ product_id: 1 });
orderProductSchema.index({ reordered: 1 });
orderProductSchema.index({ add_to_cart_order: 1 });

module.exports = mongoose.model('InstacartOrderProduct', orderProductSchema);
