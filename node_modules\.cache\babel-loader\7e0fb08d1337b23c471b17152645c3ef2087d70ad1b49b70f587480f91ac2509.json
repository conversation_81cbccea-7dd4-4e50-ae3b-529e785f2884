{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Edit, Trash2, AlertTriangle } from 'lucide-react';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport { productsAPI } from '../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n  const categories = [{\n    value: 'all',\n    label: 'All Categories'\n  }, {\n    value: 'electronics',\n    label: 'Electronics'\n  }, {\n    value: 'clothing',\n    label: 'Clothing'\n  }, {\n    value: 'books',\n    label: 'Books'\n  }, {\n    value: 'home-garden',\n    label: 'Home & Garden'\n  }, {\n    value: 'sports',\n    label: 'Sports'\n  }, {\n    value: 'toys',\n    label: 'Toys'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Inventory Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Manage your product inventory and stock levels\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowAddModal(true),\n        className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), \"Add Product\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex-1 max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filterCategory,\n              onChange: e => setFilterCategory(e.target.value),\n              className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category.value,\n                children: category.label\n              }, category.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), \"Export\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(InventoryTable, {\n        searchTerm: searchTerm,\n        filterCategory: filterCategory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), showAddModal && /*#__PURE__*/_jsxDEV(AddInventoryModal, {\n      isOpen: showAddModal,\n      onClose: () => setShowAddModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"3eTxewAkPPmB6KKtz5LAM1j35SU=\");\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plus", "Search", "Filter", "Download", "Edit", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InventoryTable", "AddInventoryModal", "productsAPI", "toast", "jsxDEV", "_jsxDEV", "Inventory", "_s", "showAddModal", "setShowAddModal", "editingProduct", "setEditingProduct", "searchTerm", "setSearchTerm", "filterCategory", "setFilterCategory", "products", "setProducts", "loading", "setLoading", "stats", "setStats", "total", "lowStock", "outOfStock", "totalValue", "categories", "value", "label", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "placeholder", "onChange", "e", "target", "map", "category", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Inventory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Download, Edit, Trash2, AlertTriangle } from 'lucide-react';\nimport InventoryTable from '../components/Inventory/InventoryTable';\nimport AddInventoryModal from '../components/Inventory/AddInventoryModal';\nimport { productsAPI } from '../services/api';\nimport toast from 'react-hot-toast';\n\nconst Inventory = () => {\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    total: 0,\n    lowStock: 0,\n    outOfStock: 0,\n    totalValue: 0\n  });\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'electronics', label: 'Electronics' },\n    { value: 'clothing', label: 'Clothing' },\n    { value: 'books', label: 'Books' },\n    { value: 'home-garden', label: 'Home & Garden' },\n    { value: 'sports', label: 'Sports' },\n    { value: 'toys', label: 'Toys' }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Inventory Management</h1>\n          <p className=\"text-gray-600\">Manage your product inventory and stock levels</p>\n        </div>\n        <button\n          onClick={() => setShowAddModal(true)}\n          className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n        >\n          <Plus className=\"h-5 w-5 mr-2\" />\n          Add Product\n        </button>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4\">\n          {/* Search */}\n          <div className=\"relative flex-1 max-w-md\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <Search className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Filter className=\"h-5 w-5 text-gray-400\" />\n              <select\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n              >\n                {categories.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Export Button */}\n            <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Inventory Table */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <InventoryTable \n          searchTerm={searchTerm}\n          filterCategory={filterCategory}\n        />\n      </div>\n\n      {/* Add Inventory Modal */}\n      {showAddModal && (\n        <AddInventoryModal\n          isOpen={showAddModal}\n          onClose={() => setShowAddModal(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,aAAa,QAAQ,cAAc;AAC1F,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC;IACjC+B,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG,CACjB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACzC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAChD;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAO,CAAC,CACjC;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzB,OAAA;MAAKwB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDzB,OAAA;QAAAyB,QAAA,gBACEzB,OAAA;UAAIwB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E7B,OAAA;UAAGwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACN7B,OAAA;QACE8B,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAC,IAAI,CAAE;QACrCoB,SAAS,EAAC,gHAAgH;QAAAC,QAAA,gBAE1HzB,OAAA,CAACZ,IAAI;UAACoC,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEzB,OAAA;QAAKwB,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAE/GzB,OAAA;UAAKwB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCzB,OAAA;YAAKwB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFzB,OAAA,CAACX,MAAM;cAACmC,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACN7B,OAAA;YACE+B,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCV,KAAK,EAAEf,UAAW;YAClB0B,QAAQ,EAAGC,CAAC,IAAK1B,aAAa,CAAC0B,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE;YAC/CE,SAAS,EAAC;UAAmN;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7B,OAAA;UAAKwB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzB,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzB,OAAA,CAACV,MAAM;cAACkC,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5C7B,OAAA;cACEsB,KAAK,EAAEb,cAAe;cACtBwB,QAAQ,EAAGC,CAAC,IAAKxB,iBAAiB,CAACwB,CAAC,CAACC,MAAM,CAACb,KAAK,CAAE;cACnDE,SAAS,EAAC,sIAAsI;cAAAC,QAAA,EAE/IJ,UAAU,CAACe,GAAG,CAAEC,QAAQ,iBACvBrC,OAAA;gBAA6BsB,KAAK,EAAEe,QAAQ,CAACf,KAAM;gBAAAG,QAAA,EAChDY,QAAQ,CAACd;cAAK,GADJc,QAAQ,CAACf,KAAK;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7B,OAAA;YAAQwB,SAAS,EAAC,gIAAgI;YAAAC,QAAA,gBAChJzB,OAAA,CAACT,QAAQ;cAACiC,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEzB,OAAA,CAACL,cAAc;QACbY,UAAU,EAAEA,UAAW;QACvBE,cAAc,EAAEA;MAAe;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL1B,YAAY,iBACXH,OAAA,CAACJ,iBAAiB;MAChB0C,MAAM,EAAEnC,YAAa;MACrBoC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,KAAK;IAAE;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3B,EAAA,CArGID,SAAS;AAAAuC,EAAA,GAATvC,SAAS;AAuGf,eAAeA,SAAS;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}