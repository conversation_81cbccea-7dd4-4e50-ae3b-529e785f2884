{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\components\\\\Layout\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { LayoutDashboard, Package, ShoppingCart, Users, BarChart3, X, Settings, LogOut, TrendingUp } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst navigation = [{\n  name: 'Dashboard',\n  href: '/dashboard',\n  icon: LayoutDashboard\n}, {\n  name: 'Inventory',\n  href: '/inventory',\n  icon: Package\n}, {\n  name: 'Sales Orders',\n  href: '/sales-orders',\n  icon: ShoppingCart\n}, {\n  name: 'Suppliers',\n  href: '/suppliers',\n  icon: Users\n}, {\n  name: 'Reports',\n  href: '/reports',\n  icon: BarChart3\n}, {\n  name: 'Market Basket Analysis',\n  href: '/instacart-analysis',\n  icon: TrendingUp\n}];\nconst Sidebar = ({\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [sidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\",\n      onClick: () => setSidebarOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 px-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Package, {\n            className: \"h-8 w-8 text-primary-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-xl font-semibold text-gray-900\",\n            children: \"Inventory Pro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"lg:hidden\",\n          onClick: () => setSidebarOpen(false),\n          children: /*#__PURE__*/_jsxDEV(X, {\n            className: \"h-6 w-6 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-8 px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2\",\n          children: navigation.map(item => {\n            const isActive = location.pathname === item.href;\n            return /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `\n                      flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\n                      ${isActive ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'}\n                    `,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: `mr-3 h-5 w-5 ${isActive ? 'text-primary-700' : 'text-gray-400'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this), item.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 19\n              }, this)\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 w-full p-4 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/settings\",\n            className: \"flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              className: \"mr-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), \"Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              className: \"mr-3 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), \"Sign Out\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Sidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "LayoutDashboard", "Package", "ShoppingCart", "Users", "BarChart3", "X", "Settings", "LogOut", "TrendingUp", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "navigation", "name", "href", "icon", "Sidebar", "sidebarOpen", "setSidebarOpen", "_s", "location", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "isActive", "pathname", "to", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/components/Layout/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport {\n  LayoutDashboard,\n  Package,\n  ShoppingCart,\n  Users,\n  BarChart3,\n  X,\n  Settings,\n  LogOut,\n  TrendingUp\n} from 'lucide-react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },\n  { name: 'Inventory', href: '/inventory', icon: Package },\n  { name: 'Sales Orders', href: '/sales-orders', icon: ShoppingCart },\n  { name: 'Suppliers', href: '/suppliers', icon: Users },\n  { name: 'Reports', href: '/reports', icon: BarChart3 },\n  { name: 'Market Basket Analysis', href: '/instacart-analysis', icon: TrendingUp },\n];\n\nconst Sidebar = ({ sidebarOpen, setSidebarOpen }) => {\n  const location = useLocation();\n\n  return (\n    <>\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <Package className=\"h-8 w-8 text-primary-600\" />\n            <span className=\"ml-2 text-xl font-semibold text-gray-900\">\n              Inventory Pro\n            </span>\n          </div>\n          <button\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          >\n            <X className=\"h-6 w-6 text-gray-400\" />\n          </button>\n        </div>\n\n        <nav className=\"mt-8 px-4\">\n          <ul className=\"space-y-2\">\n            {navigation.map((item) => {\n              const isActive = location.pathname === item.href;\n              return (\n                <li key={item.name}>\n                  <Link\n                    to={item.href}\n                    className={`\n                      flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\n                      ${isActive \n                        ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-700' \n                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                      }\n                    `}\n                  >\n                    <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-primary-700' : 'text-gray-400'}`} />\n                    {item.name}\n                  </Link>\n                </li>\n              );\n            })}\n          </ul>\n        </nav>\n\n        {/* Bottom section */}\n        <div className=\"absolute bottom-0 w-full p-4 border-t border-gray-200\">\n          <div className=\"space-y-2\">\n            <Link\n              to=\"/settings\"\n              className=\"flex items-center px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50\"\n            >\n              <Settings className=\"mr-3 h-5 w-5 text-gray-400\" />\n              Settings\n            </Link>\n            <button className=\"flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-50\">\n              <LogOut className=\"mr-3 h-5 w-5 text-gray-400\" />\n              Sign Out\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,eAAe,EACfC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,SAAS,EACTC,CAAC,EACDC,QAAQ,EACRC,MAAM,EACNC,UAAU,QACL,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEhB;AAAgB,CAAC,EAChE;EAAEc,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEf;AAAQ,CAAC,EACxD;EAAEa,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,eAAe;EAAEC,IAAI,EAAEd;AAAa,CAAC,EACnE;EAAEY,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEb;AAAM,CAAC,EACtD;EAAEW,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAEZ;AAAU,CAAC,EACtD;EAAEU,IAAI,EAAE,wBAAwB;EAAEC,IAAI,EAAE,qBAAqB;EAAEC,IAAI,EAAER;AAAW,CAAC,CAClF;AAED,MAAMS,OAAO,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACnD,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,oBACEW,OAAA,CAAAE,SAAA;IAAAU,QAAA,GAEGJ,WAAW,iBACVR,OAAA;MACEa,SAAS,EAAC,wDAAwD;MAClEC,OAAO,EAAEA,CAAA,KAAML,cAAc,CAAC,KAAK;IAAE;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,eAGDlB,OAAA;MAAKa,SAAS,EAAE;AACtB;AACA,UAAUL,WAAW,GAAG,eAAe,GAAG,mBAAmB;AAC7D,OAAQ;MAAAI,QAAA,gBACAZ,OAAA;QAAKa,SAAS,EAAC,sEAAsE;QAAAD,QAAA,gBACnFZ,OAAA;UAAKa,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAChCZ,OAAA,CAACT,OAAO;YAACsB,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDlB,OAAA;YAAMa,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAE3D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlB,OAAA;UACEa,SAAS,EAAC,WAAW;UACrBC,OAAO,EAAEA,CAAA,KAAML,cAAc,CAAC,KAAK,CAAE;UAAAG,QAAA,eAErCZ,OAAA,CAACL,CAAC;YAACkB,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAD,QAAA,eACxBZ,OAAA;UAAIa,SAAS,EAAC,WAAW;UAAAD,QAAA,EACtBT,UAAU,CAACgB,GAAG,CAAEC,IAAI,IAAK;YACxB,MAAMC,QAAQ,GAAGV,QAAQ,CAACW,QAAQ,KAAKF,IAAI,CAACf,IAAI;YAChD,oBACEL,OAAA;cAAAY,QAAA,eACEZ,OAAA,CAACZ,IAAI;gBACHmC,EAAE,EAAEH,IAAI,CAACf,IAAK;gBACdQ,SAAS,EAAE;AAC/B;AACA,wBAAwBQ,QAAQ,GACN,8DAA8D,GAC9D,oDAAoD;AAC9E,qBACsB;gBAAAT,QAAA,gBAEFZ,OAAA,CAACoB,IAAI,CAACd,IAAI;kBAACO,SAAS,EAAE,gBAAgBQ,QAAQ,GAAG,kBAAkB,GAAG,eAAe;gBAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC1FE,IAAI,CAAChB,IAAI;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC,GAbAE,IAAI,CAAChB,IAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcd,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNlB,OAAA;QAAKa,SAAS,EAAC,uDAAuD;QAAAD,QAAA,eACpEZ,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBZ,OAAA,CAACZ,IAAI;YACHmC,EAAE,EAAC,WAAW;YACdV,SAAS,EAAC,2FAA2F;YAAAD,QAAA,gBAErGZ,OAAA,CAACJ,QAAQ;cAACiB,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAErD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPlB,OAAA;YAAQa,SAAS,EAAC,kGAAkG;YAAAD,QAAA,gBAClHZ,OAAA,CAACH,MAAM;cAACgB,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACR,EAAA,CA7EIH,OAAO;EAAA,QACMlB,WAAW;AAAA;AAAAmC,EAAA,GADxBjB,OAAO;AA+Eb,eAAeA,OAAO;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}