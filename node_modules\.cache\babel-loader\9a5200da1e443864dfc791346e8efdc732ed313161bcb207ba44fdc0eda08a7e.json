{"ast": null, "code": "var _jsxFileName = \"F:\\\\SIC BigData\\\\InventoryManagement\\\\src\\\\pages\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Download, Calendar, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';\nimport { LineChart, Line, AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';\nimport ExportModal from '../components/Reports/ExportModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const [dateRange, setDateRange] = useState('30days');\n  const [reportType, setReportType] = useState('overview');\n  const [showExportModal, setShowExportModal] = useState(false);\n\n  // Sample data for charts\n  const salesData = [{\n    month: 'Jan',\n    sales: 45000,\n    orders: 120,\n    profit: 12000\n  }, {\n    month: 'Feb',\n    sales: 52000,\n    orders: 135,\n    profit: 15000\n  }, {\n    month: 'Mar',\n    sales: 48000,\n    orders: 128,\n    profit: 13500\n  }, {\n    month: 'Apr',\n    sales: 61000,\n    orders: 155,\n    profit: 18000\n  }, {\n    month: 'May',\n    sales: 55000,\n    orders: 142,\n    profit: 16500\n  }, {\n    month: 'Jun',\n    sales: 67000,\n    orders: 168,\n    profit: 20000\n  }];\n  const inventoryData = [{\n    name: 'Electronics',\n    value: 45,\n    color: '#3b82f6'\n  }, {\n    name: 'Clothing',\n    value: 25,\n    color: '#10b981'\n  }, {\n    name: 'Books',\n    value: 15,\n    color: '#f59e0b'\n  }, {\n    name: 'Home & Garden',\n    value: 10,\n    color: '#ef4444'\n  }, {\n    name: 'Sports',\n    value: 5,\n    color: '#8b5cf6'\n  }];\n  const topProducts = [{\n    name: 'iPhone 14 Pro',\n    sales: 1250,\n    revenue: 1374750\n  }, {\n    name: 'Samsung Galaxy S23',\n    sales: 980,\n    revenue: 881820\n  }, {\n    name: 'MacBook Pro M2',\n    sales: 450,\n    revenue: 899955\n  }, {\n    name: 'Nike Air Max',\n    sales: 750,\n    revenue: 112500\n  }, {\n    name: 'Adidas Ultraboost',\n    sales: 620,\n    revenue: 111600\n  }];\n  const lowStockItems = [{\n    name: 'iPhone 14 Pro',\n    current: 5,\n    minimum: 20,\n    status: 'Critical'\n  }, {\n    name: 'Nike Air Max',\n    current: 8,\n    minimum: 15,\n    status: 'Low'\n  }, {\n    name: 'Samsung Galaxy S23',\n    current: 12,\n    minimum: 25,\n    status: 'Low'\n  }, {\n    name: 'MacBook Pro M2',\n    current: 3,\n    minimum: 10,\n    status: 'Critical'\n  }];\n  const dateRanges = [{\n    value: '7days',\n    label: 'Last 7 Days'\n  }, {\n    value: '30days',\n    label: 'Last 30 Days'\n  }, {\n    value: '90days',\n    label: 'Last 90 Days'\n  }, {\n    value: '1year',\n    label: 'Last Year'\n  }];\n  const reportTypes = [{\n    value: 'overview',\n    label: 'Overview'\n  }, {\n    value: 'sales',\n    label: 'Sales Report'\n  }, {\n    value: 'inventory',\n    label: 'Inventory Report'\n  }, {\n    value: 'suppliers',\n    label: 'Supplier Report'\n  }];\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Critical':\n        return 'text-red-600 bg-red-100';\n      case 'Low':\n        return 'text-yellow-600 bg-yellow-100';\n      default:\n        return 'text-green-600 bg-green-100';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Reports & Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Analyze your business performance and trends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"select\", {\n          value: reportType,\n          onChange: e => setReportType(e.target.value),\n          className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n          children: reportTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: type.value,\n            children: type.label\n          }, type.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: dateRange,\n          onChange: e => setDateRange(e.target.value),\n          className: \"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\",\n          children: dateRanges.map(range => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: range.value,\n            children: range.label\n          }, range.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), \"Export Report\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"$348,750\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-green-50 text-green-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+12.5%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"1,248\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-blue-50 text-blue-600\",\n            children: /*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+8.2%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Avg Order Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"$279.45\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-purple-50 text-purple-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-green-600\",\n            children: \"+3.8%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-600\",\n              children: \"Profit Margin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"28.5%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-full bg-yellow-50 text-yellow-600\",\n            children: /*#__PURE__*/_jsxDEV(TrendingDown, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-red-600\",\n            children: \"-1.2%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-500 ml-2\",\n            children: \"vs last period\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Sales Trend\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(AreaChart, {\n              data: salesData,\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"sales\",\n                stroke: \"#3b82f6\",\n                fill: \"#3b82f6\",\n                fillOpacity: 0.1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Inventory Distribution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-80\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: \"100%\",\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: inventoryData,\n                cx: \"50%\",\n                cy: \"50%\",\n                outerRadius: 80,\n                dataKey: \"value\",\n                label: ({\n                  name,\n                  value\n                }) => `${name}: ${value}%`,\n                children: inventoryData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Top Selling Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Sales\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: topProducts.map((product, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: product.sales\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-4 whitespace-nowrap text-sm text-gray-900\",\n                  children: [\"$\", product.revenue.toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Low Stock Alert\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: lowStockItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-900\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"Current: \", item.current, \" | Min: \", item.minimum]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`,\n              children: item.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Profit Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-80\",\n        children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n          width: \"100%\",\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(LineChart, {\n            data: salesData,\n            children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n              strokeDasharray: \"3 3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n              dataKey: \"month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"sales\",\n              stroke: \"#3b82f6\",\n              name: \"Sales\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Line, {\n              type: \"monotone\",\n              dataKey: \"profit\",\n              stroke: \"#10b981\",\n              name: \"Profit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"nXfdedcF769NjHI9m0Hzd+GEf+A=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "Download", "Calendar", "TrendingUp", "TrendingDown", "BarChart3", "Line<PERSON>hart", "Line", "AreaChart", "Area", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "Legend", "ExportModal", "jsxDEV", "_jsxDEV", "Reports", "_s", "date<PERSON><PERSON><PERSON>", "setDateRange", "reportType", "setReportType", "showExportModal", "setShowExportModal", "salesData", "month", "sales", "orders", "profit", "inventoryData", "name", "value", "color", "topProducts", "revenue", "lowStockItems", "current", "minimum", "status", "date<PERSON><PERSON><PERSON>", "label", "reportTypes", "getStatusColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "map", "type", "range", "width", "height", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "fill", "fillOpacity", "cx", "cy", "outerRadius", "entry", "index", "product", "toLocaleString", "item", "_c", "$RefreshReg$"], "sources": ["F:/SIC BigData/InventoryManagement/src/pages/Reports.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Download, Calendar, TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';\nimport { LineChart, Line, AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';\nimport ExportModal from '../components/Reports/ExportModal';\n\nconst Reports = () => {\n  const [dateRange, setDateRange] = useState('30days');\n  const [reportType, setReportType] = useState('overview');\n  const [showExportModal, setShowExportModal] = useState(false);\n\n  // Sample data for charts\n  const salesData = [\n    { month: 'Jan', sales: 45000, orders: 120, profit: 12000 },\n    { month: 'Feb', sales: 52000, orders: 135, profit: 15000 },\n    { month: 'Mar', sales: 48000, orders: 128, profit: 13500 },\n    { month: 'Apr', sales: 61000, orders: 155, profit: 18000 },\n    { month: 'May', sales: 55000, orders: 142, profit: 16500 },\n    { month: 'Jun', sales: 67000, orders: 168, profit: 20000 }\n  ];\n\n  const inventoryData = [\n    { name: 'Electronics', value: 45, color: '#3b82f6' },\n    { name: 'Clothing', value: 25, color: '#10b981' },\n    { name: 'Books', value: 15, color: '#f59e0b' },\n    { name: 'Home & Garden', value: 10, color: '#ef4444' },\n    { name: 'Sports', value: 5, color: '#8b5cf6' }\n  ];\n\n  const topProducts = [\n    { name: 'iPhone 14 Pro', sales: 1250, revenue: 1374750 },\n    { name: 'Samsung Galaxy S23', sales: 980, revenue: 881820 },\n    { name: 'MacBook Pro M2', sales: 450, revenue: 899955 },\n    { name: 'Nike Air Max', sales: 750, revenue: 112500 },\n    { name: 'Adidas Ultraboost', sales: 620, revenue: 111600 }\n  ];\n\n  const lowStockItems = [\n    { name: 'iPhone 14 Pro', current: 5, minimum: 20, status: 'Critical' },\n    { name: 'Nike Air Max', current: 8, minimum: 15, status: 'Low' },\n    { name: 'Samsung Galaxy S23', current: 12, minimum: 25, status: 'Low' },\n    { name: 'MacBook Pro M2', current: 3, minimum: 10, status: 'Critical' }\n  ];\n\n  const dateRanges = [\n    { value: '7days', label: 'Last 7 Days' },\n    { value: '30days', label: 'Last 30 Days' },\n    { value: '90days', label: 'Last 90 Days' },\n    { value: '1year', label: 'Last Year' }\n  ];\n\n  const reportTypes = [\n    { value: 'overview', label: 'Overview' },\n    { value: 'sales', label: 'Sales Report' },\n    { value: 'inventory', label: 'Inventory Report' },\n    { value: 'suppliers', label: 'Supplier Report' }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Critical':\n        return 'text-red-600 bg-red-100';\n      case 'Low':\n        return 'text-yellow-600 bg-yellow-100';\n      default:\n        return 'text-green-600 bg-green-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Reports & Analytics</h1>\n          <p className=\"text-gray-600\">Analyze your business performance and trends</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <select\n            value={reportType}\n            onChange={(e) => setReportType(e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          >\n            {reportTypes.map((type) => (\n              <option key={type.value} value={type.value}>\n                {type.label}\n              </option>\n            ))}\n          </select>\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500\"\n          >\n            {dateRanges.map((range) => (\n              <option key={range.value} value={range.value}>\n                {range.label}\n              </option>\n            ))}\n          </select>\n          <button className=\"inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export Report\n          </button>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">$348,750</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-green-50 text-green-600\">\n              <TrendingUp className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+12.5%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n              <p className=\"text-2xl font-bold text-gray-900\">1,248</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-blue-50 text-blue-600\">\n              <BarChart3 className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+8.2%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Avg Order Value</p>\n              <p className=\"text-2xl font-bold text-gray-900\">$279.45</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-purple-50 text-purple-600\">\n              <TrendingUp className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-green-600\">+3.8%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Profit Margin</p>\n              <p className=\"text-2xl font-bold text-gray-900\">28.5%</p>\n            </div>\n            <div className=\"p-3 rounded-full bg-yellow-50 text-yellow-600\">\n              <TrendingDown className=\"h-6 w-6\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex items-center\">\n            <span className=\"text-sm font-medium text-red-600\">-1.2%</span>\n            <span className=\"text-sm text-gray-500 ml-2\">vs last period</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Sales Trend */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Sales Trend</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <AreaChart data={salesData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip />\n                <Area type=\"monotone\" dataKey=\"sales\" stroke=\"#3b82f6\" fill=\"#3b82f6\" fillOpacity={0.1} />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n\n        {/* Inventory Distribution */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Inventory Distribution</h3>\n          <div className=\"h-80\">\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\n              <PieChart>\n                <Pie\n                  data={inventoryData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={80}\n                  dataKey=\"value\"\n                  label={({ name, value }) => `${name}: ${value}%`}\n                >\n                  {inventoryData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n\n      {/* Tables Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Top Products */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Top Selling Products</h3>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Product\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Sales\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Revenue\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {topProducts.map((product, index) => (\n                  <tr key={index}>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                      {product.name}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {product.sales}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      ${product.revenue.toLocaleString()}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Low Stock Alert */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Low Stock Alert</h3>\n          <div className=\"space-y-3\">\n            {lowStockItems.map((item, index) => (\n              <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div>\n                  <p className=\"font-medium text-gray-900\">{item.name}</p>\n                  <p className=\"text-sm text-gray-500\">\n                    Current: {item.current} | Min: {item.minimum}\n                  </p>\n                </div>\n                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(item.status)}`}>\n                  {item.status}\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Profit Analysis */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Profit Analysis</h3>\n        <div className=\"h-80\">\n          <ResponsiveContainer width=\"100%\" height=\"100%\">\n            <LineChart data={salesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"month\" />\n              <YAxis />\n              <Tooltip />\n              <Legend />\n              <Line type=\"monotone\" dataKey=\"sales\" stroke=\"#3b82f6\" name=\"Sales\" />\n              <Line type=\"monotone\" dataKey=\"profit\" stroke=\"#10b981\" name=\"Profit\" />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,QAAQ,cAAc;AACtF,SAASC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,aAAa,EAAEC,OAAO,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,UAAU;AACnJ,OAAOC,WAAW,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,UAAU,CAAC;EACxD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM8B,SAAS,GAAG,CAChB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC1D;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC3D;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpD;IAAEF,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAEF,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC/C;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEH,IAAI,EAAE,eAAe;IAAEJ,KAAK,EAAE,IAAI;IAAEQ,OAAO,EAAE;EAAQ,CAAC,EACxD;IAAEJ,IAAI,EAAE,oBAAoB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EAC3D;IAAEJ,IAAI,EAAE,gBAAgB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EACvD;IAAEJ,IAAI,EAAE,cAAc;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,EACrD;IAAEJ,IAAI,EAAE,mBAAmB;IAAEJ,KAAK,EAAE,GAAG;IAAEQ,OAAO,EAAE;EAAO,CAAC,CAC3D;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEL,IAAI,EAAE,eAAe;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAW,CAAC,EACtE;IAAER,IAAI,EAAE,cAAc;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAM,CAAC,EAChE;IAAER,IAAI,EAAE,oBAAoB;IAAEM,OAAO,EAAE,EAAE;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAM,CAAC,EACvE;IAAER,IAAI,EAAE,gBAAgB;IAAEM,OAAO,EAAE,CAAC;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAW,CAAC,CACxE;EAED,MAAMC,UAAU,GAAG,CACjB;IAAER,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAc,CAAC,EACxC;IAAET,KAAK,EAAE,QAAQ;IAAES,KAAK,EAAE;EAAe,CAAC,EAC1C;IAAET,KAAK,EAAE,QAAQ;IAAES,KAAK,EAAE;EAAe,CAAC,EAC1C;IAAET,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAY,CAAC,CACvC;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEV,KAAK,EAAE,UAAU;IAAES,KAAK,EAAE;EAAW,CAAC,EACxC;IAAET,KAAK,EAAE,OAAO;IAAES,KAAK,EAAE;EAAe,CAAC,EACzC;IAAET,KAAK,EAAE,WAAW;IAAES,KAAK,EAAE;EAAmB,CAAC,EACjD;IAAET,KAAK,EAAE,WAAW;IAAES,KAAK,EAAE;EAAkB,CAAC,CACjD;EAED,MAAME,cAAc,GAAIJ,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC,KAAK,KAAK;QACR,OAAO,+BAA+B;MACxC;QACE,OAAO,6BAA6B;IACxC;EACF,CAAC;EAED,oBACEvB,OAAA;IAAK4B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB7B,OAAA;MAAK4B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD7B,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAI4B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEjC,OAAA;UAAG4B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,eACNjC,OAAA;QAAK4B,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C7B,OAAA;UACEgB,KAAK,EAAEX,UAAW;UAClB6B,QAAQ,EAAGC,CAAC,IAAK7B,aAAa,CAAC6B,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;UAC/CY,SAAS,EAAC,sIAAsI;UAAAC,QAAA,EAE/IH,WAAW,CAACW,GAAG,CAAEC,IAAI,iBACpBtC,OAAA;YAAyBgB,KAAK,EAAEsB,IAAI,CAACtB,KAAM;YAAAa,QAAA,EACxCS,IAAI,CAACb;UAAK,GADAa,IAAI,CAACtB,KAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTjC,OAAA;UACEgB,KAAK,EAAEb,SAAU;UACjB+B,QAAQ,EAAGC,CAAC,IAAK/B,YAAY,CAAC+B,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;UAC9CY,SAAS,EAAC,sIAAsI;UAAAC,QAAA,EAE/IL,UAAU,CAACa,GAAG,CAAEE,KAAK,iBACpBvC,OAAA;YAA0BgB,KAAK,EAAEuB,KAAK,CAACvB,KAAM;YAAAa,QAAA,EAC1CU,KAAK,CAACd;UAAK,GADDc,KAAK,CAACvB,KAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTjC,OAAA;UAAQ4B,SAAS,EAAC,gHAAgH;UAAAC,QAAA,gBAChI7B,OAAA,CAACpB,QAAQ;YAACgD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE7B,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAG4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClEjC,OAAA;cAAG4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,6CAA6C;YAAAC,QAAA,eAC1D7B,OAAA,CAAClB,UAAU;cAAC8C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA;YAAM4B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClEjC,OAAA;YAAM4B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAG4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEjC,OAAA;cAAG4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eACxD7B,OAAA,CAAChB,SAAS;cAAC4C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA;YAAM4B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjEjC,OAAA;YAAM4B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAG4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEjC,OAAA;cAAG4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D7B,OAAA,CAAClB,UAAU;cAAC8C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA;YAAM4B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjEjC,OAAA;YAAM4B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjC,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7B,OAAA;YAAA6B,QAAA,gBACE7B,OAAA;cAAG4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClEjC,OAAA;cAAG4B,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNjC,OAAA;YAAK4B,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC5D7B,OAAA,CAACjB,YAAY;cAAC6C,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA;YAAM4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/DjC,OAAA;YAAM4B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD7B,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAI4B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEjC,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7B,OAAA,CAACJ,mBAAmB;YAAC4C,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAZ,QAAA,eAC7C7B,OAAA,CAACb,SAAS;cAACuD,IAAI,EAAEjC,SAAU;cAAAoB,QAAA,gBACzB7B,OAAA,CAACN,aAAa;gBAACiD,eAAe,EAAC;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCjC,OAAA,CAACR,KAAK;gBAACoD,OAAO,EAAC;cAAO;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBjC,OAAA,CAACP,KAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTjC,OAAA,CAACL,OAAO;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXjC,OAAA,CAACZ,IAAI;gBAACkD,IAAI,EAAC,UAAU;gBAACM,OAAO,EAAC,OAAO;gBAACC,MAAM,EAAC,SAAS;gBAACC,IAAI,EAAC,SAAS;gBAACC,WAAW,EAAE;cAAI;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAI4B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFjC,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7B,OAAA,CAACJ,mBAAmB;YAAC4C,KAAK,EAAC,MAAM;YAACC,MAAM,EAAC,MAAM;YAAAZ,QAAA,eAC7C7B,OAAA,CAACX,QAAQ;cAAAwC,QAAA,gBACP7B,OAAA,CAACV,GAAG;gBACFoD,IAAI,EAAE5B,aAAc;gBACpBkC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACRC,WAAW,EAAE,EAAG;gBAChBN,OAAO,EAAC,OAAO;gBACfnB,KAAK,EAAEA,CAAC;kBAAEV,IAAI;kBAAEC;gBAAM,CAAC,KAAK,GAAGD,IAAI,KAAKC,KAAK,GAAI;gBAAAa,QAAA,EAEhDf,aAAa,CAACuB,GAAG,CAAC,CAACc,KAAK,EAAEC,KAAK,kBAC9BpD,OAAA,CAACT,IAAI;kBAAuBuD,IAAI,EAAEK,KAAK,CAAClC;gBAAM,GAAnC,QAAQmC,KAAK,EAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjC,OAAA,CAACL,OAAO;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpD7B,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAI4B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFjC,OAAA;UAAK4B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7B,OAAA;YAAO4B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD7B,OAAA;cAAO4B,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B7B,OAAA;gBAAA6B,QAAA,gBACE7B,OAAA;kBAAI4B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjC,OAAA;kBAAI4B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLjC,OAAA;kBAAI4B,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRjC,OAAA;cAAO4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDX,WAAW,CAACmB,GAAG,CAAC,CAACgB,OAAO,EAAED,KAAK,kBAC9BpD,OAAA;gBAAA6B,QAAA,gBACE7B,OAAA;kBAAI4B,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,EAC1EwB,OAAO,CAACtC;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACLjC,OAAA;kBAAI4B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC9DwB,OAAO,CAAC1C;gBAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLjC,OAAA;kBAAI4B,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,GAC/D,EAACwB,OAAO,CAAClC,OAAO,CAACmC,cAAc,CAAC,CAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA,GATEmB,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjC,OAAA;QAAK4B,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvE7B,OAAA;UAAI4B,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7EjC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBT,aAAa,CAACiB,GAAG,CAAC,CAACkB,IAAI,EAAEH,KAAK,kBAC7BpD,OAAA;YAAiB4B,SAAS,EAAC,6DAA6D;YAAAC,QAAA,gBACtF7B,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAG4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE0B,IAAI,CAACxC;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDjC,OAAA;gBAAG4B,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAC1B,EAAC0B,IAAI,CAAClC,OAAO,EAAC,UAAQ,EAACkC,IAAI,CAACjC,OAAO;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNjC,OAAA;cAAM4B,SAAS,EAAE,4DAA4DD,cAAc,CAAC4B,IAAI,CAAChC,MAAM,CAAC,EAAG;cAAAM,QAAA,EACxG0B,IAAI,CAAChC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GATCmB,KAAK;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjC,OAAA;MAAK4B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvE7B,OAAA;QAAI4B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EjC,OAAA;QAAK4B,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB7B,OAAA,CAACJ,mBAAmB;UAAC4C,KAAK,EAAC,MAAM;UAACC,MAAM,EAAC,MAAM;UAAAZ,QAAA,eAC7C7B,OAAA,CAACf,SAAS;YAACyD,IAAI,EAAEjC,SAAU;YAAAoB,QAAA,gBACzB7B,OAAA,CAACN,aAAa;cAACiD,eAAe,EAAC;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCjC,OAAA,CAACR,KAAK;cAACoD,OAAO,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzBjC,OAAA,CAACP,KAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACTjC,OAAA,CAACL,OAAO;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjC,OAAA,CAACH,MAAM;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVjC,OAAA,CAACd,IAAI;cAACoD,IAAI,EAAC,UAAU;cAACM,OAAO,EAAC,OAAO;cAACC,MAAM,EAAC,SAAS;cAAC9B,IAAI,EAAC;YAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEjC,OAAA,CAACd,IAAI;cAACoD,IAAI,EAAC,UAAU;cAACM,OAAO,EAAC,QAAQ;cAACC,MAAM,EAAC,SAAS;cAAC9B,IAAI,EAAC;YAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAlSID,OAAO;AAAAuD,EAAA,GAAPvD,OAAO;AAoSb,eAAeA,OAAO;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}